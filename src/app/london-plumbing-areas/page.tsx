import { createClient } from "../../prismicio";
import { notFound } from "next/navigation";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import FloatingContactWidgets from "@/components/FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import Image from "next/image";
import Location from '@/slices/ProtectingHouseholds/location_img.png';
import { PageMode } from "@/types/common";

interface PageProps {
  searchParams: { mode: PageMode }
}

async function getData() {
  try {
    const client = createClient();

    const headerQuery = await client.getByType("header");
    const footerQuery = await client.getByType("footer");
    const emergencyLocationsQuery = await client.getByType("emergency_locations");

    return {
      props: {
        header: headerQuery.results[0],
        footer: footerQuery.results[0],
        emergencyLocations: emergencyLocationsQuery.results[0].data.locations,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error);
    notFound();
  }
}

export default async function LondonPlumbingAreasPage({ searchParams }: PageProps) {
  const { props } = await getData();

  return (
    <>
      <div style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
        <Header
          emergencyLocations={props.emergencyLocations}
          header={props.header}
        />
        <main style={{ flex: 1, paddingTop: '2rem', paddingBottom: '2rem' }}>
          <Container>
            <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
              <Typography variant="h1" style={{ marginBottom: '1rem' }}>
                London Plumbing Areas
              </Typography>
              <Typography variant="body1" style={{ fontSize: '1.2rem', color: '#666' }}>
                We serve all the major London Boroughs with professional plumbing and heating services
              </Typography>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1.5rem',
              maxWidth: '1200px',
              margin: '0 auto'
            }}>
              {boroughs.map((borough: string, i: number) => (
                <div
                  key={i}
                  style={{
                    padding: '1.5rem',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    backgroundColor: '#fff',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem'
                  }}
                >
                  <Image
                    src={Location.src}
                    alt="location icon"
                    width={16}
                    height={20}
                    style={{ flexShrink: 0 }}
                  />
                  <Typography variant="body1" style={{ fontWeight: '500' }}>
                    {borough}
                  </Typography>
                </div>
              ))}
            </div>
          </Container>
        </main>
        <Footer footer={props.footer} />
      </div>
      <FloatingContactWidgets
        phoneNumber={String(props.header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
}
