"use client";

import { FC, useEffect, useMemo, useRef, useState } from "react";
import Props from "./BlogPage.types";
import { SliceZone } from "@prismicio/react";
import Container from "../Container";
import { gridSprinkle } from "@/styles/sprinkles.css";
import Header from "../Header";
import Footer from "../Footer";
import * as styles from "./BlogPage.css";
import Typography from "../Typography";
import { components } from "@/slices";
import classNames from "classnames";
import Link from "next/link";
import { parseStringToValidId } from "@/utils/helpers";
import ArrowIcon from "@/assets/icons/ArrowIcon";
import Button from "../Button";
import dayjs from "dayjs";
import { PrismicNextImage } from "@prismicio/next";
import ModeProvider from "../ModeProvider";
import { usePathname, useSearchParams } from "next/navigation";
import FloatingContactWidgets from "../FloatingContactWidgets";
import { WHATS_UP_NUMBER } from "@/utils/constants";
import { createArticleJsonLd, createBreadcrumbJsonLd } from "@/utils/metadata";

const BlogPage: FC<Props> = ({
                               header,
                               footer,
                               page
                             }) => {
  const documentRef = useRef<HTMLDivElement>(null);

  const searchParams =  useSearchParams();
  const location = usePathname();

  const isCommercial = useMemo(() => searchParams?.get("mode") === "commercial" , [searchParams]);

  const [activeHeading, setActiveHeading] = useState<string | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!documentRef.current) return;

      const documentHeadings = documentRef.current.querySelectorAll("[data-type=\"heading\"]");

      let currentHeading: Element | null = null;

      for (const heading of Array.from(documentHeadings)) {
        const headingRect = heading.getBoundingClientRect();

        if (headingRect.y < 0) {
          currentHeading = heading;
        }
      }

      if (!currentHeading) return;

      setActiveHeading(currentHeading?.getAttribute("data-type-value"));
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);


  const metadataJsonLd = useMemo(() => createArticleJsonLd({
    datePublished: new Date(page.first_publication_date).toISOString(),
    headline: page.data.title as string,
    image: (isCommercial ? page.data.image_commercial.url : page.data.image_residential.url) || "",
    pathname: location?.substring(1) || ""
  }), [page,location]);

  const metadataBreadcrumbJsonLd = useMemo(() => createBreadcrumbJsonLd({
    pathname: location?.substring(1) || "",
    title: page.data.title as string,
  }) , [page, location]);

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(metadataJsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(metadataBreadcrumbJsonLd) }}
      />
      <div
        ref={documentRef}
        className={styles.document}
      >
        <Header
          header={header}
          showModeToggle={false}
        />
        <Container
          className={styles.container}
        >
          <div
            className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
          >
            <div
              className={classNames(styles.section, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 3 }, display: "flex", justifyContent: "flex-start" }))}
            >
              <ModeProvider>
                {(mode) => (
                  <Button
                    variant="outlined"
                    color={mode === "residential" ? "secondaryInverted" : "secondary"}
                    as={Link}
                    href={mode === "residential" ? "/learn" : "/learn?mode=commercial"}
                  >
                    Back to Learn Hub
                  </Button>
                )}
              </ModeProvider>
            </div>
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 7 } })}
            >
              <div
                className={styles.articleHeader}
              >
                <div
                  className={styles.articleTags}
                >
                  {page.data.tags_group.map((item) => {
                    const { tag: { data: { name } } } = item as any;
                    return (
                      <div
                        key={name}
                        className={classNames(styles.articleTag)}
                      >
                        <Typography
                          variant="subTitleSmall"
                        >
                          {name}
                        </Typography>
                      </div>
                    );
                  })}
                </div>
                {/* | */}
                {/* <Typography
                  variant="subTitleSmall"
                >
                  {dayjs(page.last_publication_date).format("MMMM D, YYYY")}
                </Typography> */}
              </div>
              <Typography
                as="h1"
                variant="h4"
                className={styles.title}
              >
                {page.data.title}
              </Typography>
            </div>
            <div
              className={classNames(styles.menu, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 2 } }))}
            >
              {page.data.slices.filter((slice) => slice.slice_type === "heading").map((slice) => (
                <Typography
                  className={classNames(styles.menuLink, {
                    [styles.menuLinkActive]: activeHeading === String((slice.primary as any).content),
                  })}
                  as={Link}
                  key={slice.id}
                  href={parseStringToValidId(String((slice.primary as any).content), true)}
                >
                  {activeHeading === String((slice.primary as any).content) && (
                    <ArrowIcon
                      className={styles.menuIndicator}
                    />
                  )}
                  {(slice.primary as any).content}
                </Typography>
              ))}
            </div>
            <div
              className={gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 1 } })}
            />
            <div
              className={classNames(styles.content, gridSprinkle({ type: "item", cols: { mobile: 10, tablet: 5 } }))}
            >
              <div
                className={styles.image}
              >
                <PrismicNextImage
                  fill
                  objectFit="cover"
                  objectPosition="center"
                  field={isCommercial ? page.data.image_commercial : page.data.image_residential}
                />
              </div>
              <SliceZone
                slices={page.data.slices}
                components={components}
              />
            </div>
          </div>
        </Container>
        <Footer
          footer={footer}
        />
      </div>
      <FloatingContactWidgets
        phoneNumber={String(header.data.phone_number)}
        whatsAppNumber={WHATS_UP_NUMBER}
      />
    </>
  );
};

export default BlogPage;
