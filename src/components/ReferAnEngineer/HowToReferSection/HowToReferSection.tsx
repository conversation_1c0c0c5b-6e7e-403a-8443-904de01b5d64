"use client";
import React, { useState } from "react";
import * as styles from "./HowToReferSection.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import Button from "@components/Button";
import Link from "next/link";
import NextArrowIcon from "@/assets/icons/NextArrowIcon";
import { requestInfoPack } from "@/utils/api/info-pack";

const HowToReferSection = () => {
  const [email, setEmail] = useState("");
  const [showCopyToast, setShowCopyToast] = useState(false);
  const [showEmailToast, setShowEmailToast] = useState(false);
  const [showErrorToast, setShowErrorToast] = useState(false);

  const handleCopyLink = () => {
    const link = `${window.location.origin}/subcontractor-onboarding`;
    navigator.clipboard.writeText(link);
    setShowCopyToast(true);
    setTimeout(() => setShowCopyToast(false), 2000);
  };

  const handleSendInfoPack = async () => {
    if (!email.trim()) return;

    try {
      await requestInfoPack({ email: email.trim() });
      setShowEmailToast(true);
      setEmail(""); // Clean up form
      setTimeout(() => setShowEmailToast(false), 2000);
    } catch (error) {
      console.error("Error sending info pack:", error);
      setShowErrorToast(true);
      setTimeout(() => setShowErrorToast(false), 3000);
    }
  };

  return (
    <Container>
      <section className={styles.root}>
        <Typography variant="h2" className={styles.title}>
          <span className={styles.titleBold}>How to Refer</span>{" "}
          <span className={styles.titleNormal}>an Engineer</span>
        </Typography>

        <div className={styles.optionsContainer}>
          {/* Option 1: Let Them Do It */}
          <div className={styles.optionCard}>
            <Typography className={styles.optionTitle}>
              Option 1:{" "}
              <span className={styles.optionTitleBold}>Let Them Do It</span>
            </Typography>

            <Typography className={styles.optionSubtitle}>
              Go to our Sub-Contractor Sign Up Page
            </Typography>

            <div className={styles.buttonGroup}>
              <Button
                as={Link}
                isAnimated
                href="/subcontractor-onboarding"
                className={styles.signUpButton}
              >
                <span className={styles.buttonTextMobile}>Sign Up Page</span>
                <span className={styles.buttonTextDesktop}>Sub-Contractor Sign Up</span>
              </Button>

              <Button
                onClick={handleCopyLink}
                className={styles.copyButton}
                noMinWidth
              >
                Copy Link
              </Button>
            </div>

            <div className={styles.instructionText}>
              <Typography className={styles.instructionTitle}>
                When they fill out our onboarding form, they&apos;ll see a question:
              </Typography>
              <Typography className={styles.questionText}>
                &quot;Were you referred by someone?&quot;
              </Typography>
              <Typography className={styles.instructionDetail}>
                Just make sure they write{" "}
                <span className={styles.highlight}>your full name</span>. That&apos;s
                it.
              </Typography>
            </div>
          </div>

          {/* Option 2: Share Our Info Pack */}
          <div className={styles.optionCard}>
            <Typography className={styles.optionTitle}>
              Option 2:{" "}
              <span className={styles.optionTitleBold}>
                Share Our Info Pack
              </span>
            </Typography>

            <Typography className={styles.optionDescription}>
              Not ready to talk to them directly yet? No problem.
            </Typography>
            <Typography className={styles.optionDescription}>
              Pop your email in below and we&apos;ll send you a{" "}
              <span className={styles.highlight}>referral pack</span> you can
              forward.
            </Typography>

            <div className={styles.emailForm}>
              <Typography className={styles.emailLabel}>Email</Typography>
              <div className={styles.emailInputGroup}>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className={styles.emailInput}
                />
                <Button
                  style={{ width: "80px" }}
                  onClick={handleSendInfoPack}
                  className={styles.sendButton}
                  startIcon={<NextArrowIcon />}
                ></Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Toast notifications */}
      {showCopyToast && (
        <div className={`${styles.toast} ${styles.toastVisible}`}>
          Link copied to clipboard!
        </div>
      )}

      {showEmailToast && (
        <div className={`${styles.toast} ${styles.toastVisible}`}>
          Info pack sent successfully!
        </div>
      )}

      {showErrorToast && (
        <div className={`${styles.toast} ${styles.toastVisible} ${styles.toastError}`}>
          Failed to send info pack. Please try again.
        </div>
      )}
    </Container>
  );
};

export default HowToReferSection;
