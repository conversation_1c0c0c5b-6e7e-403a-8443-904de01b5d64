"use client";
import React, { useState } from "react";
import * as styles from "./UnlimitedEarningPotentialSection.css";
import Typography from "@components/Typography";
import Container from "@components/Container";

const UnlimitedEarningPotentialSection = () => {
  const [showCopyToast, setShowCopyToast] = useState(false);

  const handleCopyLink = () => {
    const link = `${window.location.origin}/subcontractor-onboarding`;
    navigator.clipboard.writeText(link);
    setShowCopyToast(true);
    setTimeout(() => setShowCopyToast(false), 2000);
  };

  return (
    <Container>
      <section className={styles.root}>
        <div className={styles.leftSection}>
          <Typography className={styles.title}>
            <span className={styles.titleHighlight}>Unlimited</span>
            <br />
            <span className={styles.titleBold}>Earning Potential</span>
          </Typography>
        </div>

        <div className={styles.rightSection}>
          <div className={styles.descriptionList}>
            <Typography className={styles.descriptionItem}>
              You can refer{" "}
              <span className={styles.highlight}>
                as many engineers as you like
              </span>
              .
            </Typography>

            <Typography className={styles.descriptionItem}>
              You&apos;ll get{" "}
              <span className={styles.highlight}>£1,000 for each one</span> —
              and another{" "}
              <span className={styles.highlight}>
                £1,000 if you top the leaderboard
              </span>
              .
            </Typography>
          </div>

          <Typography className={styles.finalText}>
            Know a WhatsApp group full of talent? Start making it rain.
          </Typography>

          <button onClick={handleCopyLink} className={styles.copyButton}>
            Copy Link
          </button>
        </div>
      </section>

      {/* Toast notification */}
      {showCopyToast && (
        <div className={`${styles.toast} ${styles.toastVisible}`}>
          Link copied to clipboard!
        </div>
      )}
    </Container>
  );
};

export default UnlimitedEarningPotentialSection;
