"use client";
import React from "react";
import * as styles from "./YourMoveSection.css";
import Typography from "@components/Typography";
import Container from "@components/Container";
import Button from "@components/Button";
import Image from "next/image";
import Link from "next/link";
import engineerImage from "./engineer.png";

const YourMoveSection = () => {
  return (
    <Container>
      <section className={styles.root}>
        <div className={styles.imageSection}>
          <Image
            src={engineerImage}
            alt="Pleasant Plumbers Engineer"
            className={styles.engineerImage}
            width={300}
            height={400}
          />
        </div>

        <div className={styles.content}>
          <Typography className={styles.title}>
            <span className={styles.titleBold}>Your</span>{" "}
            <span className={styles.titleHighlight}>Move</span>
          </Typography>

          <div className={styles.infoList}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>One friend</span>{" "}
              <span className={styles.infoValue}>= £1,000.</span>
            </div>

            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>Top of the board</span>{" "}
              <span className={styles.infoValue}>= another £1,000.</span>
            </div>
          </div>

          <Typography className={styles.finalText}>
            No limit. No nonsense. Just real cash for real referrals.
          </Typography>

          <Button
            as={Link}
            isAnimated
            href="/subcontractor-onboarding"
            className={styles.startButton}
          >
            Start Referring Now
          </Button>
        </div>
      </section>
    </Container>
  );
};

export default YourMoveSection;
