"use client";

import Container from "@components/Container";
import Typography from "@components/Typography";
import Button from "@components/Button";
import Image from "next/image";
import * as styles from "./SubcontractorAdminFocus.css";
import React from "react";
import Link from "next/link";
import engineerImage from "../../ProtectYourBusiness/components/LimitedOfferSection/engineer.png";

const SubcontractorAdminFocus = () => {
  return (
    <Container>
      <section>
        <div className={styles.card}>
          <div className={styles.imageWrapper}>
            <Image
              src={engineerImage.src}
              alt="Plumber with arms crossed"
              width={400}
              height={600}
              className={styles.image}
              priority
            />
          </div>

          <div className={styles.contentWrapper}>
            <Typography variant="h2" className={styles.title}>
              <span className={styles.mobileTitle}>
                We Handle the
                <br />
                Admin,
                <br />
                so You Can
                <br />
                <span className={styles.titleHighlight}>Focus on
                <br />
                Earning.</span>
              </span>
              <span className={styles.desktopTitle}>
                We Handle the Admin, <br/>so You Can <span className={styles.titleHighlight}>Focus on Earning.</span>
              </span>
            </Typography>

            <Button
              as={Link} href="/subcontractor-onboarding"
              className={styles.button}
              variant="filled"
              isAnimated
            >
              Become a Sub-Contractor
            </Button>
          </div>
        </div>
      </section>
    </Container>
  );
};

export default SubcontractorAdminFocus;
