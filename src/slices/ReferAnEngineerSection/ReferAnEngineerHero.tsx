"use client";
import Container from "@components/Container";
import * as styles from "./ReferAnEngineerSection.css";
import Typography from "@components/Typography";
import Button from "@components/Button";
import Link from "next/link";
import Image from "next/image";
import classNames from "classnames";
import {PrismicText} from "@prismicio/react";

const ReferAnEngineerHero = ({ slice }: any) => {
  console.log('slice', slice)
  return (
    <Container>
      <section
        className={styles.root}
      >
        <div className={styles.content}>
          <Typography
            fontFamily="primary"
            className={styles.title}
            variant="h2"
            isGreenItalic
          >
            {/*Refer an Engineer*/}
            <PrismicText field={slice.primary.title_1} />
            <p className={styles.highlight}>
              <PrismicText field={slice.primary.title_2} />
            </p>
            <PrismicText field={slice.primary.title_3} />
          </Typography>
          <Typography
            className={styles.description}
            variant="bodySmall"
          >
            <PrismicText field={slice.primary.paragraph_1} />
            <p className={classNames(styles.descriptionParagraph, styles.highlight)}>
              <PrismicText field={slice.primary.paragraph_2} />
            </p>
          </Typography>
          <div
            className={styles.buttonWrapper}
          >
            <Button
              as={Link}
              isAnimated
              href="/subcontractor-onboarding"
            >
              {slice.primary.buttontext}
            </Button>
          </div>
        </div>
        <div className={styles.imgWrapper}>
          <Image src={slice.primary.image.url} alt="Refer an Engineer" width={500} height={400} className={styles.img} />
        </div>
      </section>
    </Container>
  );
};

export default ReferAnEngineerHero;
