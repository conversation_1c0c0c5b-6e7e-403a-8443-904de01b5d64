"use client";

import Image from "next/image";
import * as styles from "./ProtectingHouseholds.css";
import Container from "@/components/Container";
import Button from "@components/Button";
import img from "./img.png";
import Location from './location_img.png';
import Typography from "@components/Typography";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

// Define types for the slice data
interface BoroughItem {
  borough: string;
}

interface ProtectingHouseholdsPrimary {
  header_anchor_name?: string;
  image?: {
    url: string;
    alt?: string;
    dimensions?: { width: number; height: number };
  };
}

interface ProtectingHouseholdsSlice {
  id: string;
  slice_type: string;
  primary: ProtectingHouseholdsPrimary;
  items: BoroughItem[];
  variation: string;
}

// Create a props interface that matches what we're actually using
export interface ProtectingHouseholdsProps {
  slice: ProtectingHouseholdsSlice;
  context?: any;
}

const ProtectingHouseholds = ({ slice, context }: ProtectingHouseholdsProps) => {
  const [screenSize, setScreenSize] = useState('mobile');
  const router = useRouter();

  const handleViewAllLocations = () => {
    router.push('/london-plumbing-areas');
  };

  // Check screen size for responsive adjustments
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      if (width >= 1920) {
        setScreenSize('ultraWide');
      } else if (width >= 1440) {
        setScreenSize('desktop');
      } else if (width >= 768) {
        setScreenSize('tablet');
      } else {
        setScreenSize('mobile');
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  // Use the boroughs from the slice if available, otherwise use the default list
  const boroughs = slice?.items?.map((item: BoroughItem) => item.borough) || [
    'Barnet', 'Bexley', 'Bromley', 'Camden', 'Croydon',
    'Greenwich', 'Hackney', 'Harrow', 'Kensington', 'Lambeth',
    'Merton', 'Redbridge', 'Sutton', 'Wandsworth', 'Westminster'
  ];

  // Adjust icon size based on screen size
  const getIconSize = () => {
    switch (screenSize) {
      case 'ultraWide':
        return { width: 16, height: 20 };
      default:
        return { width: 13, height: 16 };
    }
  };

  const iconSize = getIconSize();
  const isMobile = screenSize === 'mobile';

  // Ensure we always have a valid image source
  const imageSrc = slice?.primary?.image?.url || img.src;

  return (
    <section className={styles.root} id={slice?.primary?.header_anchor_name || undefined}>
      <Container>
        <div className={styles.contentWrapper}>
          {/* For mobile: Title, Image, Description, List, Button */}
          {isMobile ? (
            <>
              {/* 1. Title */}
              <Typography
                variant="h3"
                className={styles.title}
              >
                Your <span className={styles.italicStrong}>Trusted</span> Plumbing & Heating Partner in London
              </Typography>

              {/* 2. Image */}
              <div className={styles.imageWrapper}>
                <Image
                  src={imageSrc}
                  alt={"London"}
                  fill
                  className={styles.image}
                />
              </div>

              {/* 3. Description */}
              <div className={styles.description}>
                We serve all the major London Boroughs
              </div>

              {/* 4. Boroughs Grid (2 columns) */}
              <ul className={styles.boroughsGrid}>
                {boroughs.map((item: string, i: number) => (
                  <li key={i} className={styles.boroughItem}>
                    <Image
                      src={Location.src}
                      className={styles.locationIcon}
                      alt="location icon"
                      width={iconSize.width}
                      height={iconSize.height}
                    />
                    {item}
                  </li>
                ))}
              </ul>

              {/* 5. Button */}
              <Button
                className={styles.button}
                color="secondary"
                onClick={handleViewAllLocations}
              >
                → View All Locations
              </Button>
            </>
          ) : (
            <>
              {/* For tablet and up: Image on left, Text content on right */}
              <div className={styles.imageWrapper}>
                <Image
                  src={imageSrc}
                  alt={"London"}
                  fill
                  priority
                  className={styles.image}
                  sizes="(max-width: 768px) 100vw, (max-width: 1440px) 45vw, 40vw"
                />
              </div>

              <div className={styles.textWrapper}>
                <Typography
                  variant={'h3'}
                  className={styles.title}
                >
                  Your <span className={styles.italicStrong}>Trusted</span> Plumbing & Heating Partner in London
                </Typography>

                <p className={styles.description}>
                  We serve all the major London Boroughs
                </p>

                <ul className={styles.boroughsGrid}>
                  {boroughs.map((item: string, i: number) => (
                    <li key={i} className={styles.boroughItem}>
                      <Image
                        src={Location.src}
                        className={styles.locationIcon}
                        alt="location icon"
                        width={iconSize.width}
                        height={iconSize.height}
                      />
                      {item}
                    </li>
                  ))}
                </ul>

                <Button
                  className={styles.button}
                  color="primary"
                  as={Link}
                  href="/london-plumbing-areas"
                >
                  → View All Locations
                </Button>
              </div>
            </>
          )}
        </div>
      </Container>
    </section>
  );
};

export default ProtectingHouseholds;
