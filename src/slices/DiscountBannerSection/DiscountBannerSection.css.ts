import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const root = style({
  marginTop: 20,
  padding: 0,
});

export const container = style({
  backgroundColor: "#1A5038",
  borderRadius: 16,
  padding: 24,
  maxWidth: 800,
  width: "100%",
  minHeight: 253,
  display: "flex",
  flexDirection: "column",
  gap: 16,
  position: "relative",
  gridColumn: "span 2",
  
  "@media": {
    [breakpoints.tablet]: {
      gridColumn: "6 / 11",
    }
  }
});

export const titleRow = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  
  "@media": {
    "screen and (max-width: 768px)": {
      order: 0,
      marginBottom: 16,
    }
  }
});

export const areaTitle = style({
  color: theme.colors.primary.softWhite,
  fontSize: 24,
  fontWeight: 500,
  
  "@media": {
    "screen and (max-width: 768px)": {
      fontSize: 20,
    }
  }
});

export const titleButtons = style({
  display: "flex",
  gap: 8,
});

export const mainContent = style({
  display: "flex",
  gap: 40,
  
  "@media": {
    "screen and (max-width: 768px)": {
      flexDirection: "column",
      gap: 16,
    }
  }
});

export const imageWrapper = style({
  position: "relative",
  width: 250,
  height: 165,
  borderRadius: 24,
  overflow: "hidden",
  alignSelf: "flex-start",
  flexShrink: 0,
  
  "@media": {
    "screen and (max-width: 768px)": {
      width: "100%",
      height: 200,
      order: 1,
    }
  }
});

export const image = style({
  width: "100%",
  height: "100%",
  objectFit: "cover",
});

export const contentBlock = style({
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  flex: 1,
  
  "@media": {
    "screen and (max-width: 768px)": {
      order: 2,
      gap: 16,
    }
  }
});

export const highlightTitle = style({
  color: theme.colors.primary.softWhite,
  fontWeight: "bold",
  lineHeight: 1.5,
  opacity: 0.8,
  fontFamily: "Helvetica Neue, sans-serif",
  fontSize: 20,
});

export const description = style({
  color: theme.colors.primary.softWhite,
  opacity: 0.8,
  fontSize: 20,
});

export const descriptionDesktop = style({
  color: theme.colors.primary.softWhite,
  opacity: 0.8,
  fontSize: 20,
  
  "@media": {
    "screen and (max-width: 768px)": {
      display: "none",
    }
  }
});

export const descriptionMobile = style({
  color: theme.colors.primary.softWhite,
  opacity: 0.8,
  fontSize: 20,
  display: "none",
  
  "@media": {
    "screen and (max-width: 768px)": {
      display: "block",
    }
  }
});

export const button = style({
  backgroundColor: theme.colors.primary.asidGreen,
  alignSelf: "flex-start",
  width: "100%",
  
  ":hover": {
    backgroundColor: theme.colors.primary.asidGreenPressed,
  },
});



export const buttonText = style({
  color: theme.colors.primary.castletonGreen,
  fontWeight: 500,
});



export const titleContactButton = style({
  cursor: "pointer",
  width: 36,
  height: 36,
  borderRadius: "50%",
  backgroundColor: "#fff",
  color: theme.colors.primary.castletonGreen,
  fontSize: 18,
  border: "none",
  padding: 8,
  alignItems: "center",
  justifyContent: "center",
  lineHeight: "100%",
  display: "flex",
  textDecoration: "none",
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",
  outline: "none",

  ":hover": {
    backgroundColor: "#f5f5f5",
  },

  ":focus": {
    backgroundColor: "#f0f0f0",
    boxShadow: "0 0 0 2px rgba(26, 80, 56, 0.3)",
  },

  "@media": {
    [breakpoints.tablet]: {
      width: 42,
      height: 42,
    },
    "screen and (max-width: 768px)": {
      width: 32,
      height: 32,
      fontSize: 16,
      padding: 6,
    }
  }
});

export const contactButton = style({
  cursor: "pointer",
  width: 46,
  height: 46,
  borderRadius: "50%",
  backgroundColor: "#fff",
  color: theme.colors.primary.castletonGreen,
  fontSize: 24,
  border: "none",
  padding: 10,
  alignItems: "center",
  justifyContent: "center",
  lineHeight: "100%",
  display: "flex",
  textDecoration: "none",
  boxShadow: "rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px",

  "@media": {
    [breakpoints.tablet]: {
      width: 60,
      height: 60,
    }
  }
}); 