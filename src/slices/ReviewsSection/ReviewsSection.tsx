"use client";

import Container from "@/components/Container";
import IconButton from "@/components/IconButton";
import Star from "@/components/Star";
import Typography from "@/components/Typography";
import useIsAnimationExist from "@/hooks/useIsAnimationExist";
import useObserveIntoView from "@/hooks/useObserveIntoView";
import { gridSprinkle } from "@/styles/sprinkles.css";
import { Content } from "@prismicio/client";
import { PrismicRichText, SliceComponentProps } from "@prismicio/react";
import { assignInlineVars } from "@vanilla-extract/dynamic";
import classNames from "classnames";
import { useCallback,  useRef } from "react";
import * as styles from "./ReviewsSection.css";
import { ChevronIcon } from "@/assets/icons/ChevronIcon";
import { toCamelCase } from "@/utils/helpers";
import { SlicesContextData } from "@/types/common";

export type ReviewsSectionProps =
  SliceComponentProps<Content.ReviewsSectionSlice> & {context: SlicesContextData};

const ANIM_REVIEWS_ID = "reviews_card";
const ANIM_TITLE_ID = "reviews_title";
const ANIM_ARROWS_ID = "reviews-arrows";

// @ts-ignore
const ReviewsSection = ({ slice, context = {}}: ReviewsSectionProps): JSX.Element => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const reviewsList =context[slice.slice_type];

  const containerRef = useRef<HTMLDivElement>(null);

  const isWithAnimation = useIsAnimationExist();

  const handlePrev = () => {
    if (!scrollContainerRef.current) return;

    scrollContainerRef.current.scrollTo({
      left:
        scrollContainerRef.current.scrollLeft -
        scrollContainerRef.current.clientWidth,
    });
  };

  const handleNext = () => {
    if (!scrollContainerRef.current) return;

    scrollContainerRef.current.scrollTo({
      left:
        scrollContainerRef.current.scrollLeft +
        scrollContainerRef.current.clientWidth,
    });
  };

  const onInView = useCallback(
    (target?: HTMLElement) => {
      if (!target || !isWithAnimation) return;
      const reviewCards = target.querySelectorAll(`[data-anim-id=${ANIM_REVIEWS_ID}]`);
      const title = target.querySelector(`[data-anim-id=${ANIM_TITLE_ID}]`);
      const arrows = target.querySelector(`[data-anim-id=${ANIM_ARROWS_ID}]`);

      arrows?.classList.add(styles.control.animate);
      title?.classList.add(styles.title.animate);
      reviewCards.forEach((item) => {
        item.classList.add(styles.reviewBlock.animate);
      });
    },
    [isWithAnimation]
  );

  useObserveIntoView(containerRef, { onInView, isDisabled: !isWithAnimation });

  // const { isLoaded } = useJsApiLoader({
  //   version: "beta",
  //   id: "google-map-script",
  //   googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ?? "",
  //   libraries: ["places"],
  // });

  // useEffect(() => {
  //   if (!containerRef.current || !isLoaded) return;

  //   axios.get("/api/reviews").then((response) => {
  //     const reviews = response.data.result.reviews;

  //     console.log(reviews);
  //   });

  //   const service = new google.maps.places.PlacesService(containerRef.current);

  //   //* To get place_id for some company place use next request
  //   service.findPlaceFromQuery({
  //     query: "Pleasant Plumbers",
  //     fields: ["name","place_id"],
  //   }, (results, status) => {
  //     if (status === google.maps.places.PlacesServiceStatus.OK) {
  //       console.log(results);
  //     }
  //   });

  //   //* To get company place details using place_id use this request
  //   service.getDetails({
  //     placeId: "ChIJD2sb8E0bdkgRfYw6dxEyFM0",
  //     fields: ["name", "rating", "reviews"],
  //   }, (place, status) => {
  //     console.log(place, status);
  //   });
  // }, [isLoaded]);
  


  // TODO: To get "ratingValue" and "reviewCount" values we need to rework reviews API
  // Now we are using Places API but we need My Businees API where
  
  // const jsonLd = {
  //   // "aggregateRating": {
  //   "@context": "http://www.schema.org",
  //   "@type": "AggregateRating",
  //   "ratingValue": "4.8",
  //   "reviewCount": "85"
  //   // },
  // };

  return (
    <div
      className={classNames(styles.wrapper, {
        [styles.darkBackground]: slice.variation === "darkBackground",
      })}
    >
      {/* <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      /> */}
      <Container
        ref={containerRef}
        withAnimation={isWithAnimation}
        notFullHeight
        className={styles.container}
      >
        <section
          id={slice.primary.header_anchor_name ? toCamelCase(slice.primary.header_anchor_name) : ""}
          className={classNames(styles.root, gridSprinkle({ type: "grid" }))}
          data-slice-type={slice.slice_type}
          data-slice-variation={slice.variation}
        >
          <div
            className={gridSprinkle({
              type: "item",
              cols: { mobile: 10, tablet: 8 },
            })}
          >
            <Typography
              data-anim-id={ANIM_TITLE_ID}
              variant="h2"
              className={classNames(styles.title.base, {
                [styles.title.beforeAnimate]: isWithAnimation,
              })}
            >
              <PrismicRichText
                field={slice.primary.title}
              />
            </Typography>
          </div>
          <div
            className={gridSprinkle({ type: "item", cols: { tablet: 2 } })}
          >
            <div
              data-anim-id={ANIM_ARROWS_ID}
              className={classNames(
                styles.control.base,
                gridSprinkle({ display: { mobile: "none", tablet: "flex" } }),
                {
                  [styles.control.beforeAnimate]: isWithAnimation,
                },
              )}
            >
              <IconButton
                color={slice.variation === "darkBackground" ? "primaryInverted": "primary"}
                title="Prev Reviews"
                onClick={handlePrev}
              >
                <svg
                  width="26"
                  height="26"
                  viewBox="0 0 26 26"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M16.2353 7.80964C17.0918 6.7017 17.9401 5.28369 18.6663 3.85734H16.7761C13.5143 7.60699 10.0901 10.4222 6.50046 12.2886V13.0371L6.50046 13.0471L6.50046 13.0472L6.50046 13.8298C10.0901 15.6961 13.5143 18.5113 16.7761 22.261H18.6663C17.9401 20.8346 17.0918 19.4166 16.2353 18.3087C15.3625 17.1795 14.1179 15.8852 13.1105 14.8381L13.0935 14.8204C12.5061 14.2099 11.9903 13.6738 11.7011 13.3226L11.4742 13.0472L11.7011 12.7717C11.9918 12.4187 12.5108 11.883 13.1006 11.274L13.1184 11.2557C14.1248 10.2167 15.3651 8.93551 16.2353 7.80964Z"
                    fill="currentColor"
                  />
                </svg>
              </IconButton>
              <IconButton
                color={slice.variation === "darkBackground" ? "primaryInverted": "primary"}
                title="Next Reviews"
                onClick={handleNext}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M9.43287 7.52887C8.69381 6.57274 7.96167 5.34901 7.33496 4.1181H8.96621C11.7811 7.35398 14.7361 9.7835 17.8339 11.3941L17.8339 12.0488V12.7241C14.7361 14.3347 11.7811 16.7643 8.96621 20.0001H7.33497C7.96167 18.7692 8.69381 17.5455 9.43287 16.5894C10.1861 15.6149 11.2602 14.4979 12.1296 13.5943L12.1442 13.5791L12.1442 13.5791C12.6512 13.0522 13.0963 12.5895 13.3458 12.2865L13.5416 12.0488L13.3458 11.811C13.095 11.5064 12.6471 11.0441 12.138 10.5185L12.138 10.5185L12.1228 10.5028C11.2542 9.60611 10.1839 8.50047 9.43287 7.52887Z"
                    fill="currentColor"
                  />
                </svg>
              </IconButton>
            </div>
          </div>
          <div
            ref={scrollContainerRef}
            className={classNames(
              styles.reviewsList,
              gridSprinkle({ type: "item", cols: { mobile: 10 } })
            )}
          >
            
            {reviewsList?.reviews.map(
              // @ts-ignore
              (review, index) => (
                <blockquote
                  style={assignInlineVars({ [styles.positionIdx]: `${index}` })}
                  data-anim-id={ANIM_REVIEWS_ID}
                  tabIndex={0}
                  className={classNames(styles.reviewBlock.base, {
                    [styles.reviewBlock.beforeAnimate]: isWithAnimation
                  })}
                  key={index}
                >
                  <div
                    className={styles.reviewRating}
                  >
                    {[1, 2, 3, 4, 5].map((number) => (
                      <Star
                        key={number}
                        filled={number <= (review.rating ?? 5)}
                      />
                    ))}
                  </div>
                  <Typography
                    variant="subTitleMedium"
                    className={styles.reviewTitle}
                  >
                    {review.text.split(".")[0]}
                  </Typography>
                  <Typography
                    variant="bodySmall"
                    className={styles.reviewContent}
                  >
                    {review.text}
                  </Typography>
                  <div
                    className={styles.reviewInfo}
                  >
                    <Typography
                      variant="bodySmall"
                    >
                      <b>{review.author_name}</b>
                    </Typography>                  
                    <Typography
                      variant="note"
                    >
                      {review.relative_time_description}
                    </Typography>
                  </div>
                </blockquote>
              ))}
          </div>
          <div
            className={classNames(
              styles.control.base,
              gridSprinkle({
                type: "item",
                cols: { mobile: 10 },
                display: { mobile: "flex", tablet: "none" },
                justifyContent: "center",
              })
            )}
          >
            <IconButton
              onClick={handlePrev}
              title="Last Reviews"
              color={slice.variation === "darkBackground" ? "primaryInverted": "primary"}
            >
              <ChevronIcon
                turn={"left"}
              />
            </IconButton>
            <IconButton
              onClick={handleNext}
              title="Previous Reviews"
              color={slice.variation === "darkBackground" ? "primaryInverted": "primary"}
            >
              <ChevronIcon
                turn={"right"}
              />
            </IconButton>
          </div>
        </section>
      </Container>    
    </div>
  );
};

export default ReviewsSection;
