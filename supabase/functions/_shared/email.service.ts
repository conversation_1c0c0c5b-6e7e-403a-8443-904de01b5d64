import SendGrid from "npm:@sendgrid/mail";

export enum EmailType {
  "SUBSCRIPTION_INVOICE_PAYMENT_FAILED",
  "SUBCONTRACTOR_CREATED",
  "BOOKING_CREATED",
}

type EmailAttachment = {
  content: string; // base64
  filename: string;
  type?: string;
  disposition?: string;
};

type SendEmailOptions = {
  to: string | string[]; // Support both single email and array of emails
  from?: string;
  emailType: EmailType;
  subject?: string;
  html?: string; // allow custom html
  attachments?: EmailAttachment[]; // allow attachments
};

class EmailHtmlGenerator {
  constructor() {}

  private createSubscriptionInvoicePaymentFailedEmail(
    options: SendEmailOptions
  ): string {
    return `<html>Hey ${options.to}, your payment failed</html>`;
  }

  public createBookingCreatedEmail(bookingData: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2D5A27;">New Booking Request - Book an Expert</h2>
        <p>A new booking request has been submitted through the "Book an Expert" form.</p>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #2D5A27;">Customer Details:</h3>
          <p><strong>Name:</strong> ${bookingData.Name}</p>
          <p><strong>Email:</strong> ${bookingData.Email}</p>
          <p><strong>Phone:</strong> ${bookingData.phone}</p>
          <p><strong>Post Code:</strong> ${bookingData.post_code}</p>
          <p><strong>Service:</strong> ${bookingData.service}</p>
          <p><strong>Urgent:</strong> ${bookingData.urgent ? 'Yes' : 'No'}</p>
          ${bookingData.respond_types && bookingData.respond_types.length > 0 ?
            `<p><strong>Preferred Contact Methods:</strong> ${bookingData.respond_types.join(', ')}</p>` : ''}
          ${bookingData.ref_id ? `<p><strong>Referral ID:</strong> ${bookingData.ref_id}</p>` : ''}
        </div>
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #856404;">Customer Message:</h4>
          <p style="margin-bottom: 0;">${bookingData.message}</p>
        </div>
        <p><strong>Submitted at:</strong> ${new Date().toLocaleString()}</p>
        <p style="color: #666; font-size: 14px;">
          Please follow up with this customer as soon as possible.
        </p>
      </div>
    `;
  }

  public createEmail(options: SendEmailOptions): string {
    switch (options.emailType) {
      case EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED:
        return this.createSubscriptionInvoicePaymentFailedEmail(options);
      case EmailType.BOOKING_CREATED:
        // For booking emails, we expect the booking data to be passed in the html field
        return options.html || "Booking notification";
      default:
        return "Notification from Pleasant Plumbers";
    }
  }
}

export class EmailService {
  private htmlGenerator: EmailHtmlGenerator;
  private sendGridApiKey: string;
  private senderEmail: string;

  constructor(sendGridApiKey: string, senderEmail: string) {
    this.sendGridApiKey = sendGridApiKey;
    this.senderEmail = senderEmail;
    this.htmlGenerator = new EmailHtmlGenerator();

    SendGrid.setApiKey(this.sendGridApiKey);
  }

  private getSubjectByEmailType(type: EmailType): string {
    switch (type) {
      case EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED:
        return "Your subscription invoice payment failed - need action";
      case EmailType.BOOKING_CREATED:
        return "New Booking Request - Book an Expert";
      default:
        return "Notification from Pleasant Plumbers";
    }
  }

  private getSenderEmail(options: SendEmailOptions) {
    return options.from ?? this.senderEmail;
  }

  public createBookingNotificationHtml(bookingData: any): string {
    return this.htmlGenerator.createBookingCreatedEmail(bookingData);
  }

  public async sendEmail(options: SendEmailOptions) {
    try {
      const response = await SendGrid.send({
        to: options.to,
        from: this.getSenderEmail(options),
        subject: options.subject || this.getSubjectByEmailType(options.emailType),
        html: options.html || this.htmlGenerator.createEmail(options),
        attachments: options.attachments,
      });

      return response;
    } catch (error) {
      return {
        error: {
          message: "Failed to send email",
          details: error,
        },
      };
    }
  }
}
