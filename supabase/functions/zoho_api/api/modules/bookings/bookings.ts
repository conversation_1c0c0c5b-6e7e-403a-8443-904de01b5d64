import { env } from "../../../../_shared/env.ts";
import { <PERSON>oho<PERSON><PERSON> } from "../../zoho.ts";
import {
  CreateBookingDto,
  Booking,
  CreateZohoBookingDto,
} from "../../../types/booking.ts";
import { HttpMethod } from "../../../types/common.ts";
import AccessTokenStore from "../../../store.ts";
import {
  SupabaseClient,
  User,
} from "https://esm.sh/@supabase/supabase-js@2.39.7";
import { RefinedServiceResponse } from "../../../../_shared/common-types.ts";
import { getBookingReferralData } from "./utils.ts";
import { EmailService, EmailType } from "../../../../_shared/email.service.ts";
export class BookingsService {
  private zohoAPI: ZohoApi;
  private emailService: EmailService;

  constructor(zohoAPI: ZohoApi) {
    this.zohoAPI = zohoAPI;
    this.emailService = new EmailService(
      env.SENDGRID_API_KEY,
      env.SENDGRID_SENDER_EMAIL
    );
  }

  async create(
    dto: CreateBookingDto,
    // user: SupabaseClient | null
    user: User | null
  ): Promise<RefinedServiceResponse> {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const data: CreateZohoBookingDto = {
      Email: dto.Email,
      Name: dto.Name,
      phone: dto.phone,
      post_code: dto.post_code,
      urgent: dto.urgent,
      message: dto.message,
      respond_types: dto.respond_types,
      service: dto.service,
    };

    const { data: referral, error: getBookingMemberIdError } =
      await getBookingReferralData(user, dto);

    console.log("Referral: ", referral);

    // Something bad happened while retrieving data about correct referral data
    if (getBookingMemberIdError) {
      console.log("Get Referral error: ", getBookingMemberIdError);

      return {
        error: getBookingMemberIdError,
      };
    }

    if (referral.ref_id && referral.zohoRecordId) {
      data.ref_id = referral.ref_id;
      data.ref_member_id = referral.zohoRecordId;
    }

    const body = { data: [data] };

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/Booking`;
    const method: HttpMethod = "POST";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const created = await this.zohoAPI.call({
      endpoint,
      method,
      headers,
      body,
    });

    // Send email notification after successful Zoho booking creation
    try {
      const notificationEmail = "<EMAIL>";
      const bookingHtml = this.emailService.createBookingNotificationHtml(data);

      await this.emailService.sendEmail({
        to: [notificationEmail, "<EMAIL>"],
        emailType: EmailType.BOOKING_CREATED,
        html: bookingHtml,
      });
    } catch (emailError) {
      console.error("Failed to send booking notification email:", emailError);
      // Don't fail the booking creation if email fails
    }

    return {
      data: {
        message: "Booking created",
        details: created,
      },
    };
  }

  async find() {
    const tokenStore = AccessTokenStore.getInstance();
    const token = await tokenStore.getAccessToken();

    const endpoint = `${env.ZOHO_API_URL}/crm/v2/Booking`;
    const method: HttpMethod = "GET";
    const headers = {
      Authorization: `Zoho-oauthtoken ${token.trim()}`,
      "Content-Type": "application/json",
    };

    const response = await this.zohoAPI.call({ endpoint, method, headers });

    if (!response) {
      throw new Error("Failed to get response");
    }

    const data = response.data;

    if (!data || !Array.isArray(data)) {
      throw new Error("Failed to fetch bookings");
    }

    const bookings: Booking[] = data.map((item) => {
      return {
        id: item.id ?? null,
        Email: item.Email ?? null,
        Name: item.Name ?? null,
        phone: item.phone ?? null,
        post_code: item.post_code ?? null,
        respond_types: item.respond_types ?? null,
        urgent: item.urgent ?? null,
        message: item.message ?? null,
        ref_id: item.ref_id ?? null,
        service: item.service ?? null,
      };
    });

    return bookings;
  }
}
