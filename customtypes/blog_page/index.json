{"format": "page", "id": "blog_page", "label": "Blog Page", "repeatable": true, "status": true, "json": {"Main": {"uid": {"config": {"label": "UID"}, "type": "UID"}, "title": {"type": "Text", "config": {"label": "Title", "placeholder": ""}}, "reading_time_minutes": {"type": "Number", "config": {"label": "Reading Time Minutes", "placeholder": ""}}, "theme": {"type": "Select", "config": {"label": "Theme", "placeholder": "", "options": ["Heating", "Plumbing"]}}, "page_mode": {"type": "Select", "config": {"label": "Page mode", "placeholder": "Commercial, Residential or Both", "options": ["both", "commercial", "residential"], "default_value": "both"}}, "tags_group": {"type": "Group", "config": {"label": "Tags group", "repeat": true, "fields": {"tag": {"type": "Link", "config": {"label": "Tag", "select": "document", "customtypes": ["blog_tags"]}}}}}, "slices": {"type": "Slices", "fieldset": "Slice Zone", "config": {"choices": {"discount_banner_section": {"type": "SharedSlice"}, "image": {"type": "SharedSlice"}, "paragraph": {"type": "SharedSlice"}, "heading": {"type": "SharedSlice"}, "block_with_title_and_content": {"type": "SharedSlice"}}}}}, "SEO & Metadata": {"title_commercial": {"type": "Text", "config": {"label": "Title Commercial", "placeholder": ""}}, "description_commercial": {"type": "Text", "config": {"label": "Description Commercial", "placeholder": ""}}, "image_commercial": {"type": "Image", "config": {"label": "Image Commercial", "constraint": {}, "thumbnails": []}}, "title_residential": {"type": "Text", "config": {"label": "Title Residential", "placeholder": ""}}, "description_residential": {"type": "Text", "config": {"label": "Description Residential", "placeholder": ""}}, "image_residential": {"type": "Image", "config": {"label": "Image Residential", "constraint": {}, "thumbnails": []}}, "no_index_residential": {"type": "Boolean", "config": {"label": "No Index Residential", "default_value": false, "placeholder_true": "true", "placeholder_false": "false"}}, "no_follow_residential": {"type": "Boolean", "config": {"label": "No Follow Residential", "default_value": false, "placeholder_true": "true", "placeholder_false": "false"}}, "no_index_commercial": {"type": "Boolean", "config": {"label": "No Index Commercial", "default_value": false, "placeholder_true": "true", "placeholder_false": "false"}}, "no_follow_commercial": {"type": "Boolean", "config": {"label": "No Follow Commercial", "default_value": false, "placeholder_true": "true", "placeholder_false": "false"}}}}}