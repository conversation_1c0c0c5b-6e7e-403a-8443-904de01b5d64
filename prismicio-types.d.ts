// Code generated by Slice Machine. DO NOT EDIT.

import type * as prismic from "@prismicio/client";

type Simplify<T> = { [KeyType in keyof T]: T[KeyType] };

/**
 * Item in *Blog Page → Tags group*
 */
export interface BlogPageDocumentDataTagsGroupItem {
  /**
   * Tag field in *Blog Page → Tags group*
   *
   * - **Field Type**: Content Relationship
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.tags_group[].tag
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  tag: prismic.ContentRelationshipField<"blog_tags">;
}

type BlogPageDocumentDataSlicesSlice =
  | DiscountBannerSectionSlice
  | ImageSlice
  | ParagraphSlice
  | HeadingSlice
  | BlockWithTitleAndContentSlice;

/**
 * Content for Blog Page documents
 */
interface BlogPageDocumentData {
  /**
   * Title field in *Blog Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.title
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title: prismic.KeyTextField;

  /**
   * Reading Time Minutes field in *Blog Page*
   *
   * - **Field Type**: Number
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.reading_time_minutes
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#number
   */
  reading_time_minutes: prismic.NumberField;

  /**
   * Theme field in *Blog Page*
   *
   * - **Field Type**: Select
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.theme
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#select
   */
  theme: prismic.SelectField<"Heating" | "Plumbing">;

  /**
   * Page mode field in *Blog Page*
   *
   * - **Field Type**: Select
   * - **Placeholder**: Commercial, Residential or Both
   * - **Default Value**: both
   * - **API ID Path**: blog_page.page_mode
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#select
   */
  page_mode: prismic.SelectField<
    "both" | "commercial" | "residential",
    "filled"
  >;

  /**
   * Tags group field in *Blog Page*
   *
   * - **Field Type**: Group
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.tags_group[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#group
   */
  tags_group: prismic.GroupField<Simplify<BlogPageDocumentDataTagsGroupItem>>;

  /**
   * Slice Zone field in *Blog Page*
   *
   * - **Field Type**: Slice Zone
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.slices[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#slices
   */
  slices: prismic.SliceZone<BlogPageDocumentDataSlicesSlice> /**
   * Title Commercial field in *Blog Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.title_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */;
  title_commercial: prismic.KeyTextField;

  /**
   * Description Commercial field in *Blog Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.description_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_commercial: prismic.KeyTextField;

  /**
   * Image Commercial field in *Blog Page*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.image_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image_commercial: prismic.ImageField<never>;

  /**
   * Title Residential field in *Blog Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.title_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title_residential: prismic.KeyTextField;

  /**
   * Description Residential field in *Blog Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.description_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_residential: prismic.KeyTextField;

  /**
   * Image Residential field in *Blog Page*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_page.image_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image_residential: prismic.ImageField<never>;

  /**
   * No Index Residential field in *Blog Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: blog_page.no_index_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_index_residential: prismic.BooleanField;

  /**
   * No Follow Residential field in *Blog Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: blog_page.no_follow_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_follow_residential: prismic.BooleanField;

  /**
   * No Index Commercial field in *Blog Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: blog_page.no_index_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_index_commercial: prismic.BooleanField;

  /**
   * No Follow Commercial field in *Blog Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: blog_page.no_follow_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_follow_commercial: prismic.BooleanField;
}

/**
 * Blog Page document from Prismic
 *
 * - **API ID**: `blog_page`
 * - **Repeatable**: `true`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type BlogPageDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithUID<
    Simplify<BlogPageDocumentData>,
    "blog_page",
    Lang
  >;

/**
 * Content for Blog Tags documents
 */
interface BlogTagsDocumentData {
  /**
   * Name field in *Blog Tags*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: blog_tags.name
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  name: prismic.KeyTextField;
}

/**
 * Blog Tags document from Prismic
 *
 * - **API ID**: `blog_tags`
 * - **Repeatable**: `true`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type BlogTagsDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithUID<
    Simplify<BlogTagsDocumentData>,
    "blog_tags",
    Lang
  >;

/**
 * Item in *Emergency locations → Locations*
 */
export interface EmergencyLocationsDocumentDataLocationsItem {
  /**
   * Place id field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].place_id
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  place_id: prismic.KeyTextField;

  /**
   * Location name field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].location_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  location_name: prismic.KeyTextField;

  /**
   * uuid field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].uuid
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  uuid: prismic.KeyTextField;

  /**
   * Label field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: London
   * - **API ID Path**: emergency_locations.locations[].label
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  label: prismic.KeyTextField;

  /**
   * Location field in *Emergency locations → Locations*
   *
   * - **Field Type**: GeoPoint
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].location
   * - **Documentation**: https://prismic.io/docs/field#geopoint
   */
  location: prismic.GeoPointField;

  /**
   * Work time field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: Open, 10am to 7pm
   * - **API ID Path**: emergency_locations.locations[].work_time
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  work_time: prismic.KeyTextField;

  /**
   * Phone number field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: (*************
   * - **API ID Path**: emergency_locations.locations[].phone_number
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  phone_number: prismic.KeyTextField;

  /**
   * Location image field in *Emergency locations → Locations*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].location_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  location_image: prismic.ImageField<never>;

  /**
   * Marker image field in *Emergency locations → Locations*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].marker_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  marker_image: prismic.ImageField<never>;

  /**
   * Meta Title field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].meta_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  meta_title: prismic.KeyTextField;

  /**
   * Meta Description field in *Emergency locations → Locations*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].meta_description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  meta_description: prismic.KeyTextField;

  /**
   * Meta Image field in *Emergency locations → Locations*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[].meta_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  meta_image: prismic.ImageField<never>;

  /**
   * No Follow field in *Emergency locations → Locations*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: emergency_locations.locations[].no_follow
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_follow: prismic.BooleanField;

  /**
   * No Index field in *Emergency locations → Locations*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: emergency_locations.locations[].no_index
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_index: prismic.BooleanField;
}

type EmergencyLocationsDocumentDataSlicesSlice =
  | PhoneAnimationSectionSlice
  | ReviewsSectionSlice
  | DividerSlice
  | FaqsSectionSlice
  | GasServicesSectionSlice
  | HeatServicesSectionSlice
  | MarqueeSectionSlice
  | LocateUsSectionSlice
  | PlumbingServicesSectionSlice
  | CentralHeatingControlsHeroSlice;

/**
 * Content for Emergency locations documents
 */
interface EmergencyLocationsDocumentData {
  /**
   * Locations field in *Emergency locations*
   *
   * - **Field Type**: Group
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.locations[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#group
   */
  locations: prismic.GroupField<
    Simplify<EmergencyLocationsDocumentDataLocationsItem>
  >;

  /**
   * Slice Zone field in *Emergency locations*
   *
   * - **Field Type**: Slice Zone
   * - **Placeholder**: *None*
   * - **API ID Path**: emergency_locations.slices[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#slices
   */
  slices: prismic.SliceZone<EmergencyLocationsDocumentDataSlicesSlice>;
}

/**
 * Emergency locations document from Prismic
 *
 * - **API ID**: `emergency_locations`
 * - **Repeatable**: `false`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type EmergencyLocationsDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithoutUID<
    Simplify<EmergencyLocationsDocumentData>,
    "emergency_locations",
    Lang
  >;

/**
 * Item in *Footer → Menu*
 */
export interface FooterDocumentDataMenuItem {
  /**
   * Menu Item Name field in *Footer → Menu*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.menu[].menu_item_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  menu_item_name: prismic.KeyTextField;

  /**
   * Menu Item Link field in *Footer → Menu*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.menu[].menu_item_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  menu_item_link: prismic.LinkField;

  /**
   * Parent Menu Item Name field in *Footer → Menu*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.menu[].parent_menu_item_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  parent_menu_item_name: prismic.KeyTextField;
}

/**
 * Item in *Footer → Accreditations items*
 */
export interface FooterDocumentDataAccreditationsItemsItem {
  /**
   * Accreditation image field in *Footer → Accreditations items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.accreditations_items[].accreditation_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  accreditation_image: prismic.ImageField<never>;

  /**
   * Accreditation link field in *Footer → Accreditations items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.accreditations_items[].accreditation_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  accreditation_link: prismic.LinkField;
}

/**
 * Content for Footer documents
 */
interface FooterDocumentData {
  /**
   * Phone Number field in *Footer*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.phone_number
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  phone_number: prismic.KeyTextField;

  /**
   * Address field in *Footer*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.address
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  address: prismic.KeyTextField;

  /**
   * Email field in *Footer*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.email
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  email: prismic.KeyTextField;

  /**
   * Menu field in *Footer*
   *
   * - **Field Type**: Group
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.menu[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#group
   */
  menu: prismic.GroupField<Simplify<FooterDocumentDataMenuItem>>;

  /**
   * LinkedIn Link field in *Footer*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.linkedin_link
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  linkedin_link: prismic.LinkField;

  /**
   * Facebook Link field in *Footer*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.facebook_link
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  facebook_link: prismic.LinkField;

  /**
   * Instagram Link field in *Footer*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.instagram_link
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  instagram_link: prismic.LinkField;

  /**
   * YouTube Link field in *Footer*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.youtube_link
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  youtube_link: prismic.LinkField;

  /**
   * Accreditations title field in *Footer*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.accreditations_title
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  accreditations_title: prismic.RichTextField;

  /**
   * Accreditations items field in *Footer*
   *
   * - **Field Type**: Group
   * - **Placeholder**: *None*
   * - **API ID Path**: footer.accreditations_items[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#group
   */
  accreditations_items: prismic.GroupField<
    Simplify<FooterDocumentDataAccreditationsItemsItem>
  >;
}

/**
 * Footer document from Prismic
 *
 * - **API ID**: `footer`
 * - **Repeatable**: `false`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type FooterDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithoutUID<
    Simplify<FooterDocumentData>,
    "footer",
    Lang
  >;

/**
 * Item in *Header → Navigation Menu*
 */
export interface HeaderDocumentDataNavigationMenuItem {
  /**
   * Menu Item Name field in *Header → Navigation Menu*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: header.navigation_menu[].menu_item_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  menu_item_name: prismic.KeyTextField;

  /**
   * Menu Item Link field in *Header → Navigation Menu*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: header.navigation_menu[].menu_item_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  menu_item_link: prismic.LinkField;

  /**
   * Parent Menu Item Name field in *Header → Navigation Menu*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: header.navigation_menu[].parent_menu_item_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  parent_menu_item_name: prismic.KeyTextField;

  /**
   * Relate to field in *Header → Navigation Menu*
   *
   * - **Field Type**: Content Relationship
   * - **Placeholder**: *None*
   * - **API ID Path**: header.navigation_menu[].relate_to
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  relate_to: prismic.ContentRelationshipField;
}

/**
 * Content for Header documents
 */
interface HeaderDocumentData {
  /**
   * Navigation Menu field in *Header*
   *
   * - **Field Type**: Group
   * - **Placeholder**: *None*
   * - **API ID Path**: header.navigation_menu[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#group
   */
  navigation_menu: prismic.GroupField<
    Simplify<HeaderDocumentDataNavigationMenuItem>
  >;

  /**
   * Phone Number field in *Header*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: header.phone_number
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  phone_number: prismic.KeyTextField;
}

/**
 * Header document from Prismic
 *
 * - **API ID**: `header`
 * - **Repeatable**: `false`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type HeaderDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithoutUID<
    Simplify<HeaderDocumentData>,
    "header",
    Lang
  >;

type LandingPageDocumentDataSlicesSlice =
  | RecentWorkSlice
  | ServicesSectionSlice
  | ProtectingHouseholdsSectionSlice
  | LondonPlumbingAreasSlice
  | ProtectingHouseholdsSlice
  | ContactUsSlice
  | TopServicesSlice
  | MapSectionSlice
  | BlogSectionSlice
  | DividerSlice
  | VideoAskSectionSlice
  | BlockWithMoviesSlice
  | BannerSectionSlice
  | BenefitsSectionSlice
  | ArrivalTimeSectionSlice
  | DocumentWithNavigationAndAccordionsSlice
  | ListWithSwitchSlice
  | DocumentWithNavigationSectionSlice
  | HeroWithListSlice
  | ApplianceSectionSlice
  | StepsSectionSlice
  | TextAndImageSectionSlice
  | CompareFeaturesSectionSlice
  | SubscribtionsSectionSlice
  | PlansSectionSlice
  | FaqsSectionSlice
  | ReviewsSectionSlice
  | BecomeMemberSectionSlice
  | PartnersSectionSlice
  | ApproachSectionSlice
  | PreviewSectionSlice;

type LandingPageDocumentDataSlices2Slice =
  | ContactUsSlice
  | TopServicesSlice
  | DividerSlice
  | VideoAskSectionSlice
  | PreviewSectionSlice
  | HeroWithListSlice
  | ApproachSectionSlice
  | TextAndImageSectionSlice
  | BecomeMemberSectionSlice
  | ReviewsSectionSlice
  | BannerSectionSlice
  | SubscribtionsSectionSlice
  | CompareFeaturesSectionSlice
  | PartnersSectionSlice
  | StepsSectionSlice
  | PlansSectionSlice
  | ListWithSwitchSlice
  | DocumentWithNavigationSectionSlice
  | DocumentWithNavigationAndAccordionsSlice
  | BlockWithMoviesSlice
  | FaqsSectionSlice
  | ArrivalTimeSectionSlice
  | BenefitsSectionSlice
  | ApplianceSectionSlice;

/**
 * Content for Landing Page documents
 */
interface LandingPageDocumentData {
  /**
   * Slice Zone field in *Landing Page*
   *
   * - **Field Type**: Slice Zone
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.slices[]
   * - **Tab**: Residental
   * - **Documentation**: https://prismic.io/docs/field#slices
   */
  slices: prismic.SliceZone<LandingPageDocumentDataSlicesSlice> /**
   * Title Residential field in *Landing Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.title_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */;
  title_residential: prismic.KeyTextField;

  /**
   * Description Residential field in *Landing Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.description_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_residential: prismic.KeyTextField;

  /**
   * Image Residential field in *Landing Page*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.image_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image_residential: prismic.ImageField<never>;

  /**
   * Title Commercial field in *Landing Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.title_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title_commercial: prismic.KeyTextField;

  /**
   * Description Commercial field in *Landing Page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.description_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_commercial: prismic.KeyTextField;

  /**
   * Image Commercial field in *Landing Page*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.image_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image_commercial: prismic.ImageField<never>;

  /**
   * No Index Residential field in *Landing Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: landing_page.no_index_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_index_residential: prismic.BooleanField;

  /**
   * No Follow Residential field in *Landing Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: landing_page.no_follow_residential
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_follow_residential: prismic.BooleanField;

  /**
   * No Index Commercial field in *Landing Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: landing_page.no_index_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_index_commercial: prismic.BooleanField;

  /**
   * No Follow Commercial field in *Landing Page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: landing_page.no_follow_commercial
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_follow_commercial: prismic.BooleanField /**
   * Slice Zone field in *Landing Page*
   *
   * - **Field Type**: Slice Zone
   * - **Placeholder**: *None*
   * - **API ID Path**: landing_page.slices2[]
   * - **Tab**: Commercial
   * - **Documentation**: https://prismic.io/docs/field#slices
   */;
  slices2: prismic.SliceZone<LandingPageDocumentDataSlices2Slice>;
}

/**
 * Landing Page document from Prismic
 *
 * - **API ID**: `landing_page`
 * - **Repeatable**: `true`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type LandingPageDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithUID<
    Simplify<LandingPageDocumentData>,
    "landing_page",
    Lang
  >;

type ReferPageDocumentDataSlicesSlice =
  | ReferralBonusSectionSlice
  | ReferAnEngineerSectionSlice
  | BoilerServiceHeroSlice
  | DividerSlice
  | PeacePlanSubscriptionBenefitsSlice
  | PeacePlanCtaSlice
  | PeacePlanAdvantagesSlice
  | CentralHeatingControlsHeroSlice
  | UpgrageYourChCSlice
  | TopBrandsSectionSlice
  | ReferAndEarnSlice
  | ChSUpgradesSectionSlice
  | HelpAndAdviceSectionSlice
  | HorizontalListSectionSlice
  | AdvantagesSectionSlice
  | CardsWithImgOnHoverSlice
  | ShuffledCardsListSlice
  | PeacePlanDescriptionWithTogglerSlice
  | BecomeMemCardsSlice
  | ReviewsSectionSlice
  | FaqsSectionSlice;

/**
 * Content for Refer page documents
 */
interface ReferPageDocumentData {
  /**
   * Slice Zone field in *Refer page*
   *
   * - **Field Type**: Slice Zone
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_page.slices[]
   * - **Tab**: Main
   * - **Documentation**: https://prismic.io/docs/field#slices
   */
  slices: prismic.SliceZone<ReferPageDocumentDataSlicesSlice> /**
   * Meta Description field in *Refer page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: A brief summary of the page
   * - **API ID Path**: refer_page.meta_description
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */;
  meta_description: prismic.KeyTextField;

  /**
   * Meta Image field in *Refer page*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_page.meta_image
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  meta_image: prismic.ImageField<never>;

  /**
   * Meta Title field in *Refer page*
   *
   * - **Field Type**: Text
   * - **Placeholder**: A title of the page used for social media and search engines
   * - **API ID Path**: refer_page.meta_title
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  meta_title: prismic.KeyTextField;

  /**
   * No Index field in *Refer page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: refer_page.no_index
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_index: prismic.BooleanField;

  /**
   * No Follow field in *Refer page*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: refer_page.no_follow
   * - **Tab**: SEO & Metadata
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  no_follow: prismic.BooleanField;
}

/**
 * Refer page document from Prismic
 *
 * - **API ID**: `refer_page`
 * - **Repeatable**: `true`
 * - **Documentation**: https://prismic.io/docs/custom-types
 *
 * @typeParam Lang - Language API ID of the document.
 */
export type ReferPageDocument<Lang extends string = string> =
  prismic.PrismicDocumentWithUID<
    Simplify<ReferPageDocumentData>,
    "refer_page",
    Lang
  >;

export type AllDocumentTypes =
  | BlogPageDocument
  | BlogTagsDocument
  | EmergencyLocationsDocument
  | FooterDocument
  | HeaderDocument
  | LandingPageDocument
  | ReferPageDocument;

/**
 * Primary content in *AdvantagesSection → Primary*
 */
export interface AdvantagesSectionSliceDefaultPrimary {
  /**
   * Title field in *AdvantagesSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Hero field in *AdvantagesSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.primary.hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;

  /**
   * Hero2 field in *AdvantagesSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.primary.hero2
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero2: prismic.ImageField<never>;

  /**
   * Header anchor name field in *AdvantagesSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *AdvantagesSection → Items*
 */
export interface AdvantagesSectionSliceDefaultItem {
  /**
   * Title field in *AdvantagesSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *AdvantagesSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.items[].description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Icon field in *AdvantagesSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: advantages_section.items[].icon
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  icon: prismic.ImageField<never>;
}

/**
 * Default variation for AdvantagesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type AdvantagesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<AdvantagesSectionSliceDefaultPrimary>,
  Simplify<AdvantagesSectionSliceDefaultItem>
>;

/**
 * Slice variation for *AdvantagesSection*
 */
type AdvantagesSectionSliceVariation = AdvantagesSectionSliceDefault;

/**
 * AdvantagesSection Shared Slice
 *
 * - **API ID**: `advantages_section`
 * - **Description**: AdvantagesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type AdvantagesSectionSlice = prismic.SharedSlice<
  "advantages_section",
  AdvantagesSectionSliceVariation
>;

/**
 * Primary content in *ApplianceSection → Primary*
 */
export interface ApplianceSectionSliceDefaultPrimary {
  /**
   * Title field in *ApplianceSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: appliance_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *ApplianceSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: appliance_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *ApplianceSection → Items*
 */
export interface ApplianceSectionSliceDefaultItem {
  /**
   * Appliance Item field in *ApplianceSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: appliance_section.items[].appliance_item
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  appliance_item: prismic.RichTextField;
}

/**
 * Default variation for ApplianceSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ApplianceSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ApplianceSectionSliceDefaultPrimary>,
  Simplify<ApplianceSectionSliceDefaultItem>
>;

/**
 * Slice variation for *ApplianceSection*
 */
type ApplianceSectionSliceVariation = ApplianceSectionSliceDefault;

/**
 * ApplianceSection Shared Slice
 *
 * - **API ID**: `appliance_section`
 * - **Description**: ApplianceSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ApplianceSectionSlice = prismic.SharedSlice<
  "appliance_section",
  ApplianceSectionSliceVariation
>;

/**
 * Primary content in *ApproachSection → Primary*
 */
export interface ApproachSectionSliceDefaultPrimary {
  /**
   * Title field in *ApproachSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: approach_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Preview field in *ApproachSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: approach_section.primary.preview
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview: prismic.ImageField<never>;
}

/**
 * Primary content in *ApproachSection → Items*
 */
export interface ApproachSectionSliceDefaultItem {
  /**
   * ApproachTitle field in *ApproachSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: approach_section.items[].approachtitle
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  approachtitle: prismic.KeyTextField;

  /**
   * ApproachDescription field in *ApproachSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: approach_section.items[].approachdescription
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  approachdescription: prismic.RichTextField;

  /**
   * ApproachIcon field in *ApproachSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: approach_section.items[].approachIcon
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  approachIcon: prismic.ImageField<never>;
}

/**
 * Default variation for ApproachSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ApproachSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ApproachSectionSliceDefaultPrimary>,
  Simplify<ApproachSectionSliceDefaultItem>
>;

/**
 * Slice variation for *ApproachSection*
 */
type ApproachSectionSliceVariation = ApproachSectionSliceDefault;

/**
 * ApproachSection Shared Slice
 *
 * - **API ID**: `approach_section`
 * - **Description**: ApproachSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ApproachSectionSlice = prismic.SharedSlice<
  "approach_section",
  ApproachSectionSliceVariation
>;

/**
 * Primary content in *ArrivalTimeSection → Primary*
 */
export interface ArrivalTimeSectionSliceDefaultPrimary {
  /**
   * Title field in *ArrivalTimeSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: arrival_time_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Member wait time field in *ArrivalTimeSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: arrival_time_section.primary.member_wait_time
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  member_wait_time: prismic.KeyTextField;

  /**
   * Non member wait time field in *ArrivalTimeSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: arrival_time_section.primary.non_member_wait_time
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  non_member_wait_time: prismic.KeyTextField;

  /**
   * Wait time field in *ArrivalTimeSection → Primary*
   *
   * - **Field Type**: Number
   * - **Placeholder**: *None*
   * - **API ID Path**: arrival_time_section.primary.wait_time
   * - **Documentation**: https://prismic.io/docs/field#number
   */
  wait_time: prismic.NumberField;
}

/**
 * Default variation for ArrivalTimeSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ArrivalTimeSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ArrivalTimeSectionSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *ArrivalTimeSection*
 */
type ArrivalTimeSectionSliceVariation = ArrivalTimeSectionSliceDefault;

/**
 * ArrivalTimeSection Shared Slice
 *
 * - **API ID**: `arrival_time_section`
 * - **Description**: ArrivalTimeSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ArrivalTimeSectionSlice = prismic.SharedSlice<
  "arrival_time_section",
  ArrivalTimeSectionSliceVariation
>;

/**
 * Primary content in *BannerSection → Primary*
 */
export interface BannerSectionSliceDefaultPrimary {
  /**
   * Title field in *BannerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BannerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Action Text field in *BannerSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.action_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  action_text: prismic.KeyTextField;

  /**
   * Action Link field in *BannerSection → Primary*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.action_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  action_link: prismic.LinkField;

  /**
   * Preview field in *BannerSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.preview
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview: prismic.ImageField<never>;
}

/**
 * Default variation for BannerSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BannerSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BannerSectionSliceDefaultPrimary>,
  never
>;

/**
 * Primary content in *BannerSection → Primary*
 */
export interface BannerSectionSliceTextOnlyPrimary {
  /**
   * Title field in *BannerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BannerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: banner_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Text Only variation for BannerSection Slice
 *
 * - **API ID**: `textOnly`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BannerSectionSliceTextOnly = prismic.SharedSliceVariation<
  "textOnly",
  Simplify<BannerSectionSliceTextOnlyPrimary>,
  never
>;

/**
 * Slice variation for *BannerSection*
 */
type BannerSectionSliceVariation =
  | BannerSectionSliceDefault
  | BannerSectionSliceTextOnly;

/**
 * BannerSection Shared Slice
 *
 * - **API ID**: `banner_section`
 * - **Description**: BannerSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BannerSectionSlice = prismic.SharedSlice<
  "banner_section",
  BannerSectionSliceVariation
>;

/**
 * Primary content in *BecomeMemCards → Primary*
 */
export interface BecomeMemCardsSliceDefaultPrimary {
  /**
   * Title field in *BecomeMemCards → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BecomeMemCards → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Card title field in *BecomeMemCards → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.primary.card_title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  card_title: prismic.RichTextField;

  /**
   * Hero field in *BecomeMemCards → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.primary.hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;

  /**
   * Header anchor name field in *BecomeMemCards → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *BecomeMemCards → Items*
 */
export interface BecomeMemCardsSliceDefaultItem {
  /**
   * Title field in *BecomeMemCards → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BecomeMemCards → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_mem_cards.items[].description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;
}

/**
 * Default variation for BecomeMemCards Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BecomeMemCardsSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BecomeMemCardsSliceDefaultPrimary>,
  Simplify<BecomeMemCardsSliceDefaultItem>
>;

/**
 * Slice variation for *BecomeMemCards*
 */
type BecomeMemCardsSliceVariation = BecomeMemCardsSliceDefault;

/**
 * BecomeMemCards Shared Slice
 *
 * - **API ID**: `become_mem_cards`
 * - **Description**: BecomeMemCards
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BecomeMemCardsSlice = prismic.SharedSlice<
  "become_mem_cards",
  BecomeMemCardsSliceVariation
>;

/**
 * Primary content in *BecomeMemberSection → Primary*
 */
export interface BecomeMemberSectionSliceDefaultPrimary {
  /**
   * Title field in *BecomeMemberSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Subtitle field in *BecomeMemberSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.primary.subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;

  /**
   * Preview field in *BecomeMemberSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.primary.preview
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview: prismic.ImageField<never>;
}

/**
 * Primary content in *BecomeMemberSection → Items*
 */
export interface BecomeMemberSectionSliceDefaultItem {
  /**
   * Point field in *BecomeMemberSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.items[].point
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  point: prismic.RichTextField;
}

/**
 * Default variation for BecomeMemberSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BecomeMemberSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BecomeMemberSectionSliceDefaultPrimary>,
  Simplify<BecomeMemberSectionSliceDefaultItem>
>;

/**
 * Primary content in *BecomeMemberSection → Primary*
 */
export interface BecomeMemberSectionSliceAlternativePrimary {
  /**
   * Title field in *BecomeMemberSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Subtitle field in *BecomeMemberSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.primary.subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;

  /**
   * Preview field in *BecomeMemberSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.primary.preview
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview: prismic.ImageField<never>;
}

/**
 * Primary content in *BecomeMemberSection → Items*
 */
export interface BecomeMemberSectionSliceAlternativeItem {
  /**
   * Point field in *BecomeMemberSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: become_member_section.items[].point
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  point: prismic.RichTextField;
}

/**
 * Alternative variation for BecomeMemberSection Slice
 *
 * - **API ID**: `alternative`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BecomeMemberSectionSliceAlternative = prismic.SharedSliceVariation<
  "alternative",
  Simplify<BecomeMemberSectionSliceAlternativePrimary>,
  Simplify<BecomeMemberSectionSliceAlternativeItem>
>;

/**
 * Slice variation for *BecomeMemberSection*
 */
type BecomeMemberSectionSliceVariation =
  | BecomeMemberSectionSliceDefault
  | BecomeMemberSectionSliceAlternative;

/**
 * BecomeMemberSection Shared Slice
 *
 * - **API ID**: `become_member_section`
 * - **Description**: BecomeMemberSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BecomeMemberSectionSlice = prismic.SharedSlice<
  "become_member_section",
  BecomeMemberSectionSliceVariation
>;

/**
 * Primary content in *BenefitsSection → Primary*
 */
export interface BenefitsSectionSliceDefaultPrimary {
  /**
   * Title field in *BenefitsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: benefits_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BenefitsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: benefits_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *BenefitsSection → Items*
 */
export interface BenefitsSectionSliceDefaultItem {
  /**
   * Benefit Title field in *BenefitsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: benefits_section.items[].benefit_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  benefit_title: prismic.KeyTextField;

  /**
   * Benefit Content field in *BenefitsSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: benefits_section.items[].benefit_content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  benefit_content: prismic.RichTextField;
}

/**
 * Default variation for BenefitsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BenefitsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BenefitsSectionSliceDefaultPrimary>,
  Simplify<BenefitsSectionSliceDefaultItem>
>;

/**
 * Slice variation for *BenefitsSection*
 */
type BenefitsSectionSliceVariation = BenefitsSectionSliceDefault;

/**
 * BenefitsSection Shared Slice
 *
 * - **API ID**: `benefits_section`
 * - **Description**: BenefitsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BenefitsSectionSlice = prismic.SharedSlice<
  "benefits_section",
  BenefitsSectionSliceVariation
>;

/**
 * Primary content in *BlockWithMovies → Primary*
 */
export interface BlockWithMoviesSliceDefaultPrimary {
  /**
   * Title field in *BlockWithMovies → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_movies.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BlockWithMovies → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_movies.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *BlockWithMovies → Items*
 */
export interface BlockWithMoviesSliceDefaultItem {
  /**
   * Movie Preview field in *BlockWithMovies → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_movies.items[].movie_preview
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  movie_preview: prismic.ImageField<never>;

  /**
   * Movie field in *BlockWithMovies → Items*
   *
   * - **Field Type**: Link to Media
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_movies.items[].movie
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  movie: prismic.LinkToMediaField;

  /**
   * Movie Description field in *BlockWithMovies → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_movies.items[].movie_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  movie_description: prismic.RichTextField;

  /**
   * Movie Title field in *BlockWithMovies → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_movies.items[].movie_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  movie_title: prismic.KeyTextField;
}

/**
 * Default variation for BlockWithMovies Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BlockWithMoviesSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BlockWithMoviesSliceDefaultPrimary>,
  Simplify<BlockWithMoviesSliceDefaultItem>
>;

/**
 * Slice variation for *BlockWithMovies*
 */
type BlockWithMoviesSliceVariation = BlockWithMoviesSliceDefault;

/**
 * BlockWithMovies Shared Slice
 *
 * - **API ID**: `block_with_movies`
 * - **Description**: BlockWithMovies
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BlockWithMoviesSlice = prismic.SharedSlice<
  "block_with_movies",
  BlockWithMoviesSliceVariation
>;

/**
 * Primary content in *BlockWithTitleAndContent → Primary*
 */
export interface BlockWithTitleAndContentSliceDefaultPrimary {
  /**
   * Title field in *BlockWithTitleAndContent → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_title_and_content.primary.title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title: prismic.KeyTextField;

  /**
   * Content field in *BlockWithTitleAndContent → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: block_with_title_and_content.primary.content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  content: prismic.RichTextField;
}

/**
 * Default variation for BlockWithTitleAndContent Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BlockWithTitleAndContentSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BlockWithTitleAndContentSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *BlockWithTitleAndContent*
 */
type BlockWithTitleAndContentSliceVariation =
  BlockWithTitleAndContentSliceDefault;

/**
 * BlockWithTitleAndContent Shared Slice
 *
 * - **API ID**: `block_with_title_and_content`
 * - **Description**: BlockWithTitleAndContent
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BlockWithTitleAndContentSlice = prismic.SharedSlice<
  "block_with_title_and_content",
  BlockWithTitleAndContentSliceVariation
>;

/**
 * Default variation for BlogSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BlogSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Record<string, never>,
  never
>;

/**
 * Slice variation for *BlogSection*
 */
type BlogSectionSliceVariation = BlogSectionSliceDefault;

/**
 * BlogSection Shared Slice
 *
 * - **API ID**: `blog_section`
 * - **Description**: BlogSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BlogSectionSlice = prismic.SharedSlice<
  "blog_section",
  BlogSectionSliceVariation
>;

/**
 * Primary content in *BoilerServiceHero → Primary*
 */
export interface BoilerServiceHeroSliceDefaultPrimary {
  /**
   * Title field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Button text field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.button_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  button_text: prismic.KeyTextField;

  /**
   * Image field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;
}

/**
 * Default variation for BoilerServiceHero Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BoilerServiceHeroSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<BoilerServiceHeroSliceDefaultPrimary>,
  never
>;

/**
 * Primary content in *BoilerServiceHero → Primary*
 */
export interface BoilerServiceHeroSliceImageOnTheRightPrimary {
  /**
   * Title field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Button text field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.button_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  button_text: prismic.KeyTextField;

  /**
   * Image field in *BoilerServiceHero → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: boiler_service_hero.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;
}

/**
 * Image on the right variation for BoilerServiceHero Slice
 *
 * - **API ID**: `imageOnTheRight`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BoilerServiceHeroSliceImageOnTheRight =
  prismic.SharedSliceVariation<
    "imageOnTheRight",
    Simplify<BoilerServiceHeroSliceImageOnTheRightPrimary>,
    never
  >;

/**
 * Slice variation for *BoilerServiceHero*
 */
type BoilerServiceHeroSliceVariation =
  | BoilerServiceHeroSliceDefault
  | BoilerServiceHeroSliceImageOnTheRight;

/**
 * BoilerServiceHero Shared Slice
 *
 * - **API ID**: `boiler_service_hero`
 * - **Description**: BoilerServiceHero
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type BoilerServiceHeroSlice = prismic.SharedSlice<
  "boiler_service_hero",
  BoilerServiceHeroSliceVariation
>;

/**
 * Primary content in *CardsWithImgOnHover → Primary*
 */
export interface CardsWithImgOnHoverSliceDefaultPrimary {
  /**
   * Title field in *CardsWithImgOnHover → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *CardsWithImgOnHover → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Footer title field in *CardsWithImgOnHover → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.primary.footer_title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  footer_title: prismic.RichTextField;

  /**
   * Footer description field in *CardsWithImgOnHover → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.primary.footer_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  footer_description: prismic.RichTextField;

  /**
   * Header anchor name field in *CardsWithImgOnHover → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *CardsWithImgOnHover → Items*
 */
export interface CardsWithImgOnHoverSliceDefaultItem {
  /**
   * Title field in *CardsWithImgOnHover → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *CardsWithImgOnHover → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.items[].description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Card img field in *CardsWithImgOnHover → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: cards_with_img_on_hover.items[].card_img
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  card_img: prismic.ImageField<never>;
}

/**
 * Default variation for CardsWithImgOnHover Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CardsWithImgOnHoverSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<CardsWithImgOnHoverSliceDefaultPrimary>,
  Simplify<CardsWithImgOnHoverSliceDefaultItem>
>;

/**
 * Slice variation for *CardsWithImgOnHover*
 */
type CardsWithImgOnHoverSliceVariation = CardsWithImgOnHoverSliceDefault;

/**
 * CardsWithImgOnHover Shared Slice
 *
 * - **API ID**: `cards_with_img_on_hover`
 * - **Description**: CardsWithImgOnHover
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CardsWithImgOnHoverSlice = prismic.SharedSlice<
  "cards_with_img_on_hover",
  CardsWithImgOnHoverSliceVariation
>;

/**
 * Primary content in *CentralHeatingControlsHero → Primary*
 */
export interface CentralHeatingControlsHeroSliceDefaultPrimary {
  /**
   * Title field in *CentralHeatingControlsHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: central_heating_controls_hero.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *CentralHeatingControlsHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: central_heating_controls_hero.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Image field in *CentralHeatingControlsHero → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: central_heating_controls_hero.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;
}

/**
 * Default variation for CentralHeatingControlsHero Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CentralHeatingControlsHeroSliceDefault =
  prismic.SharedSliceVariation<
    "default",
    Simplify<CentralHeatingControlsHeroSliceDefaultPrimary>,
    never
  >;

/**
 * Primary content in *CentralHeatingControlsHero → Primary*
 */
export interface CentralHeatingControlsHeroSliceCallUsPrimary {
  /**
   * Title field in *CentralHeatingControlsHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: central_heating_controls_hero.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *CentralHeatingControlsHero → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: central_heating_controls_hero.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Image field in *CentralHeatingControlsHero → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: central_heating_controls_hero.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;
}

/**
 * Call us variation for CentralHeatingControlsHero Slice
 *
 * - **API ID**: `callUs`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CentralHeatingControlsHeroSliceCallUs =
  prismic.SharedSliceVariation<
    "callUs",
    Simplify<CentralHeatingControlsHeroSliceCallUsPrimary>,
    never
  >;

/**
 * Slice variation for *CentralHeatingControlsHero*
 */
type CentralHeatingControlsHeroSliceVariation =
  | CentralHeatingControlsHeroSliceDefault
  | CentralHeatingControlsHeroSliceCallUs;

/**
 * CentralHeatingControlsHero Shared Slice
 *
 * - **API ID**: `central_heating_controls_hero`
 * - **Description**: CentralHeatingControlsHero
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CentralHeatingControlsHeroSlice = prismic.SharedSlice<
  "central_heating_controls_hero",
  CentralHeatingControlsHeroSliceVariation
>;

/**
 * Primary content in *ChSUpgradesSection → Primary*
 */
export interface ChSUpgradesSectionSliceDefaultPrimary {
  /**
   * Title field in *ChSUpgradesSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: ch_s_upgrades_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *ChSUpgradesSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: ch_s_upgrades_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *ChSUpgradesSection → Items*
 */
export interface ChSUpgradesSectionSliceDefaultItem {
  /**
   * Title field in *ChSUpgradesSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: ch_s_upgrades_section.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Image field in *ChSUpgradesSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: ch_s_upgrades_section.items[].image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Dark green bg field in *ChSUpgradesSection → Items*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: ch_s_upgrades_section.items[].dark_green_bg
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  dark_green_bg: prismic.BooleanField;
}

/**
 * Default variation for ChSUpgradesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ChSUpgradesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ChSUpgradesSectionSliceDefaultPrimary>,
  Simplify<ChSUpgradesSectionSliceDefaultItem>
>;

/**
 * Slice variation for *ChSUpgradesSection*
 */
type ChSUpgradesSectionSliceVariation = ChSUpgradesSectionSliceDefault;

/**
 * ChSUpgradesSection Shared Slice
 *
 * - **API ID**: `ch_s_upgrades_section`
 * - **Description**: ChSUpgradesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ChSUpgradesSectionSlice = prismic.SharedSlice<
  "ch_s_upgrades_section",
  ChSUpgradesSectionSliceVariation
>;

/**
 * Primary content in *CompareFeaturesSection → Primary*
 */
export interface CompareFeaturesSectionSliceDefaultPrimary {
  /**
   * Title field in *CompareFeaturesSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: compare_features_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;
}

/**
 * Default variation for CompareFeaturesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CompareFeaturesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<CompareFeaturesSectionSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *CompareFeaturesSection*
 */
type CompareFeaturesSectionSliceVariation = CompareFeaturesSectionSliceDefault;

/**
 * CompareFeaturesSection Shared Slice
 *
 * - **API ID**: `compare_features_section`
 * - **Description**: CompareFeaturesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type CompareFeaturesSectionSlice = prismic.SharedSlice<
  "compare_features_section",
  CompareFeaturesSectionSliceVariation
>;

/**
 * Primary content in *ContactUs → Primary*
 */
export interface ContactUsSliceDefaultPrimary {
  /**
   * Title field in *ContactUs → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: contact_us.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *ContactUs → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: contact_us.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Left image field in *ContactUs → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: contact_us.primary.left_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  left_image: prismic.ImageField<never>;

  /**
   * Right image field in *ContactUs → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: contact_us.primary.right_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  right_image: prismic.ImageField<never>;
}

/**
 * Default variation for ContactUs Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ContactUsSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ContactUsSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *ContactUs*
 */
type ContactUsSliceVariation = ContactUsSliceDefault;

/**
 * ContactUs Shared Slice
 *
 * - **API ID**: `contact_us`
 * - **Description**: ContactUs
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ContactUsSlice = prismic.SharedSlice<
  "contact_us",
  ContactUsSliceVariation
>;

/**
 * Primary content in *DiscountBannerSection → Primary*
 */
export interface DiscountBannerSectionSliceDefaultPrimary {
  /**
   * Image field in *DiscountBannerSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: discount_banner_section.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Area Title field in *DiscountBannerSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: discount_banner_section.primary.area_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  area_title: prismic.KeyTextField;

  /**
   * Highlight Title field in *DiscountBannerSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: discount_banner_section.primary.highlight_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  highlight_title: prismic.KeyTextField;

  /**
   * Description field in *DiscountBannerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: discount_banner_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Button Label field in *DiscountBannerSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: discount_banner_section.primary.button_label
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  button_label: prismic.KeyTextField;
}

/**
 * Default variation for DiscountBannerSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DiscountBannerSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<DiscountBannerSectionSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *DiscountBannerSection*
 */
type DiscountBannerSectionSliceVariation = DiscountBannerSectionSliceDefault;

/**
 * DiscountBannerSection Shared Slice
 *
 * - **API ID**: `discount_banner_section`
 * - **Description**: DiscountBannerSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DiscountBannerSectionSlice = prismic.SharedSlice<
  "discount_banner_section",
  DiscountBannerSectionSliceVariation
>;

/**
 * Default variation for Divider Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DividerSliceDefault = prismic.SharedSliceVariation<
  "default",
  Record<string, never>,
  never
>;

/**
 * Slice variation for *Divider*
 */
type DividerSliceVariation = DividerSliceDefault;

/**
 * Divider Shared Slice
 *
 * - **API ID**: `divider`
 * - **Description**: Divider
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DividerSlice = prismic.SharedSlice<
  "divider",
  DividerSliceVariation
>;

/**
 * Primary content in *DocumentWithNavigationAndAccordions → Primary*
 */
export interface DocumentWithNavigationAndAccordionsSliceDefaultPrimary {
  /**
   * Title field in *DocumentWithNavigationAndAccordions → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_and_accordions.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *DocumentWithNavigationAndAccordions → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_and_accordions.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *DocumentWithNavigationAndAccordions → Items*
 */
export interface DocumentWithNavigationAndAccordionsSliceDefaultItem {
  /**
   * Accordion Title field in *DocumentWithNavigationAndAccordions → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_and_accordions.items[].accordion_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  accordion_title: prismic.KeyTextField;

  /**
   * Accordion Content field in *DocumentWithNavigationAndAccordions → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_and_accordions.items[].accordion_content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  accordion_content: prismic.RichTextField;

  /**
   * Accordion Group Name field in *DocumentWithNavigationAndAccordions → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_and_accordions.items[].accordion_group_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  accordion_group_name: prismic.KeyTextField;
}

/**
 * Default variation for DocumentWithNavigationAndAccordions Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DocumentWithNavigationAndAccordionsSliceDefault =
  prismic.SharedSliceVariation<
    "default",
    Simplify<DocumentWithNavigationAndAccordionsSliceDefaultPrimary>,
    Simplify<DocumentWithNavigationAndAccordionsSliceDefaultItem>
  >;

/**
 * Slice variation for *DocumentWithNavigationAndAccordions*
 */
type DocumentWithNavigationAndAccordionsSliceVariation =
  DocumentWithNavigationAndAccordionsSliceDefault;

/**
 * DocumentWithNavigationAndAccordions Shared Slice
 *
 * - **API ID**: `document_with_navigation_and_accordions`
 * - **Description**: DocumentWithNavigationAndAccordions
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DocumentWithNavigationAndAccordionsSlice = prismic.SharedSlice<
  "document_with_navigation_and_accordions",
  DocumentWithNavigationAndAccordionsSliceVariation
>;

/**
 * Primary content in *DocumentWithNavigationSection → Primary*
 */
export interface DocumentWithNavigationSectionSliceDefaultPrimary {
  /**
   * Title field in *DocumentWithNavigationSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Last Update field in *DocumentWithNavigationSection → Primary*
   *
   * - **Field Type**: Date
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_section.primary.last_update
   * - **Documentation**: https://prismic.io/docs/field#date
   */
  last_update: prismic.DateField;

  /**
   * Description field in *DocumentWithNavigationSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *DocumentWithNavigationSection → Items*
 */
export interface DocumentWithNavigationSectionSliceDefaultItem {
  /**
   * Block Title field in *DocumentWithNavigationSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_section.items[].block_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  block_title: prismic.KeyTextField;

  /**
   * Block Content field in *DocumentWithNavigationSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: document_with_navigation_section.items[].block_content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  block_content: prismic.RichTextField;
}

/**
 * Default variation for DocumentWithNavigationSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DocumentWithNavigationSectionSliceDefault =
  prismic.SharedSliceVariation<
    "default",
    Simplify<DocumentWithNavigationSectionSliceDefaultPrimary>,
    Simplify<DocumentWithNavigationSectionSliceDefaultItem>
  >;

/**
 * Slice variation for *DocumentWithNavigationSection*
 */
type DocumentWithNavigationSectionSliceVariation =
  DocumentWithNavigationSectionSliceDefault;

/**
 * DocumentWithNavigationSection Shared Slice
 *
 * - **API ID**: `document_with_navigation_section`
 * - **Description**: DocumentWithNavigationSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type DocumentWithNavigationSectionSlice = prismic.SharedSlice<
  "document_with_navigation_section",
  DocumentWithNavigationSectionSliceVariation
>;

/**
 * Primary content in *FaqsSection → Primary*
 */
export interface FaqsSectionSliceDefaultPrimary {
  /**
   * Title field in *FaqsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: faqs_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *FaqsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: faqs_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Header anchor name field in *FaqsSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: faqs_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *FaqsSection → Items*
 */
export interface FaqsSectionSliceDefaultItem {
  /**
   * Question field in *FaqsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: faqs_section.items[].question
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  question: prismic.KeyTextField;

  /**
   * Answer field in *FaqsSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: faqs_section.items[].answer
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  answer: prismic.RichTextField;
}

/**
 * Default variation for FaqsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type FaqsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<FaqsSectionSliceDefaultPrimary>,
  Simplify<FaqsSectionSliceDefaultItem>
>;

/**
 * Slice variation for *FaqsSection*
 */
type FaqsSectionSliceVariation = FaqsSectionSliceDefault;

/**
 * FaqsSection Shared Slice
 *
 * - **API ID**: `faqs_section`
 * - **Description**: FaqsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type FaqsSectionSlice = prismic.SharedSlice<
  "faqs_section",
  FaqsSectionSliceVariation
>;

/**
 * Primary content in *GasServicesSection → Primary*
 */
export interface GasServicesSectionSliceDefaultPrimary {
  /**
   * Title field in *GasServicesSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: gas_services_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Left image field in *GasServicesSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: gas_services_section.primary.left_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  left_image: prismic.ImageField<never>;

  /**
   * Right image field in *GasServicesSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: gas_services_section.primary.right_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  right_image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *GasServicesSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: gas_services_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *GasServicesSection → Items*
 */
export interface GasServicesSectionSliceDefaultItem {
  /**
   * Service field in *GasServicesSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: gas_services_section.items[].service
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  service: prismic.RichTextField;
}

/**
 * Default variation for GasServicesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type GasServicesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<GasServicesSectionSliceDefaultPrimary>,
  Simplify<GasServicesSectionSliceDefaultItem>
>;

/**
 * Slice variation for *GasServicesSection*
 */
type GasServicesSectionSliceVariation = GasServicesSectionSliceDefault;

/**
 * GasServicesSection Shared Slice
 *
 * - **API ID**: `gas_services_section`
 * - **Description**: GasServicesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type GasServicesSectionSlice = prismic.SharedSlice<
  "gas_services_section",
  GasServicesSectionSliceVariation
>;

/**
 * Primary content in *Heading → Primary*
 */
export interface HeadingSliceDefaultPrimary {
  /**
   * Content field in *Heading → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: heading.primary.content
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  content: prismic.KeyTextField;
}

/**
 * Default variation for Heading Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeadingSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<HeadingSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *Heading*
 */
type HeadingSliceVariation = HeadingSliceDefault;

/**
 * Heading Shared Slice
 *
 * - **API ID**: `heading`
 * - **Description**: Heading
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeadingSlice = prismic.SharedSlice<
  "heading",
  HeadingSliceVariation
>;

/**
 * Primary content in *HeatServicesSection → Primary*
 */
export interface HeatServicesSectionSliceDefaultPrimary {
  /**
   * Title field in *HeatServicesSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: heat_services_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *HeatServicesSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: heat_services_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *HeatServicesSection → Items*
 */
export interface HeatServicesSectionSliceDefaultItem {
  /**
   * Image field in *HeatServicesSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: heat_services_section.items[].image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * List items field in *HeatServicesSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: Boiler Breakdown,Boiler Losing Pressure
   * - **API ID Path**: heat_services_section.items[].list_items
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  list_items: prismic.KeyTextField;

  /**
   * Dark green bg field in *HeatServicesSection → Items*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: heat_services_section.items[].dark_green_bg
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  dark_green_bg: prismic.BooleanField;
}

/**
 * Default variation for HeatServicesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeatServicesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<HeatServicesSectionSliceDefaultPrimary>,
  Simplify<HeatServicesSectionSliceDefaultItem>
>;

/**
 * Slice variation for *HeatServicesSection*
 */
type HeatServicesSectionSliceVariation = HeatServicesSectionSliceDefault;

/**
 * HeatServicesSection Shared Slice
 *
 * - **API ID**: `heat_services_section`
 * - **Description**: HeatServicesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeatServicesSectionSlice = prismic.SharedSlice<
  "heat_services_section",
  HeatServicesSectionSliceVariation
>;

/**
 * Primary content in *HelpAndAdviceSection → Primary*
 */
export interface HelpAndAdviceSectionSliceDefaultPrimary {
  /**
   * Title field in *HelpAndAdviceSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: help_and_advice_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *HelpAndAdviceSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: help_and_advice_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *HelpAndAdviceSection → Items*
 */
export interface HelpAndAdviceSectionSliceDefaultItem {
  /**
   * Title field in *HelpAndAdviceSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: help_and_advice_section.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Link to field in *HelpAndAdviceSection → Items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: help_and_advice_section.items[].link_to
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  link_to: prismic.LinkField;

  /**
   * Tags field in *HelpAndAdviceSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: Boiler,Service,Help
   * - **API ID Path**: help_and_advice_section.items[].tags
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  tags: prismic.KeyTextField;

  /**
   * Hero field in *HelpAndAdviceSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: help_and_advice_section.items[].hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;
}

/**
 * Default variation for HelpAndAdviceSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HelpAndAdviceSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<HelpAndAdviceSectionSliceDefaultPrimary>,
  Simplify<HelpAndAdviceSectionSliceDefaultItem>
>;

/**
 * Slice variation for *HelpAndAdviceSection*
 */
type HelpAndAdviceSectionSliceVariation = HelpAndAdviceSectionSliceDefault;

/**
 * HelpAndAdviceSection Shared Slice
 *
 * - **API ID**: `help_and_advice_section`
 * - **Description**: HelpAndAdviceSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HelpAndAdviceSectionSlice = prismic.SharedSlice<
  "help_and_advice_section",
  HelpAndAdviceSectionSliceVariation
>;

/**
 * Primary content in *HeroWithList → Primary*
 */
export interface HeroWithListSliceDefaultPrimary {
  /**
   * Title field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Hero field in *HeroWithList → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;
}

/**
 * Primary content in *HeroWithList → Items*
 */
export interface HeroWithListSliceDefaultItem {
  /**
   * Item field in *HeroWithList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.items[].item
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  item: prismic.KeyTextField;
}

/**
 * Default variation for HeroWithList Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeroWithListSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<HeroWithListSliceDefaultPrimary>,
  Simplify<HeroWithListSliceDefaultItem>
>;

/**
 * Primary content in *HeroWithList → Primary*
 */
export interface HeroWithListSliceAlternativeColorsPrimary {
  /**
   * Title field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Hero field in *HeroWithList → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;
}

/**
 * Primary content in *HeroWithList → Items*
 */
export interface HeroWithListSliceAlternativeColorsItem {
  /**
   * Item field in *HeroWithList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.items[].item
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  item: prismic.KeyTextField;
}

/**
 * AlternativeColors variation for HeroWithList Slice
 *
 * - **API ID**: `alternativeColors`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeroWithListSliceAlternativeColors = prismic.SharedSliceVariation<
  "alternativeColors",
  Simplify<HeroWithListSliceAlternativeColorsPrimary>,
  Simplify<HeroWithListSliceAlternativeColorsItem>
>;

/**
 * Primary content in *HeroWithList → Primary*
 */
export interface HeroWithListSliceInvertedPrimary {
  /**
   * Title field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Hero field in *HeroWithList → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;
}

/**
 * Primary content in *HeroWithList → Items*
 */
export interface HeroWithListSliceInvertedItem {
  /**
   * Item field in *HeroWithList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.items[].item
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  item: prismic.KeyTextField;
}

/**
 * Inverted variation for HeroWithList Slice
 *
 * - **API ID**: `inverted`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeroWithListSliceInverted = prismic.SharedSliceVariation<
  "inverted",
  Simplify<HeroWithListSliceInvertedPrimary>,
  Simplify<HeroWithListSliceInvertedItem>
>;

/**
 * Primary content in *HeroWithList → Primary*
 */
export interface HeroWithListSliceInvertedAlternativePrimary {
  /**
   * Title field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *HeroWithList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Hero field in *HeroWithList → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.primary.hero
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  hero: prismic.ImageField<never>;
}

/**
 * Primary content in *HeroWithList → Items*
 */
export interface HeroWithListSliceInvertedAlternativeItem {
  /**
   * Item field in *HeroWithList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: hero_with_list.items[].item
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  item: prismic.KeyTextField;
}

/**
 * InvertedAlternative variation for HeroWithList Slice
 *
 * - **API ID**: `invertedAlternative`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeroWithListSliceInvertedAlternative = prismic.SharedSliceVariation<
  "invertedAlternative",
  Simplify<HeroWithListSliceInvertedAlternativePrimary>,
  Simplify<HeroWithListSliceInvertedAlternativeItem>
>;

/**
 * Slice variation for *HeroWithList*
 */
type HeroWithListSliceVariation =
  | HeroWithListSliceDefault
  | HeroWithListSliceAlternativeColors
  | HeroWithListSliceInverted
  | HeroWithListSliceInvertedAlternative;

/**
 * HeroWithList Shared Slice
 *
 * - **API ID**: `hero_with_list`
 * - **Description**: HeroWithList
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HeroWithListSlice = prismic.SharedSlice<
  "hero_with_list",
  HeroWithListSliceVariation
>;

/**
 * Primary content in *HorizontalListSection → Primary*
 */
export interface HorizontalListSectionSliceDefaultPrimary {
  /**
   * Title field in *HorizontalListSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: horizontal_list_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;
}

/**
 * Primary content in *HorizontalListSection → Items*
 */
export interface HorizontalListSectionSliceDefaultItem {
  /**
   * Title field in *HorizontalListSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: horizontal_list_section.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *HorizontalListSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: horizontal_list_section.items[].description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Default variation for HorizontalListSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HorizontalListSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<HorizontalListSectionSliceDefaultPrimary>,
  Simplify<HorizontalListSectionSliceDefaultItem>
>;

/**
 * Slice variation for *HorizontalListSection*
 */
type HorizontalListSectionSliceVariation = HorizontalListSectionSliceDefault;

/**
 * HorizontalListSection Shared Slice
 *
 * - **API ID**: `horizontal_list_section`
 * - **Description**: HorizontalListSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type HorizontalListSectionSlice = prismic.SharedSlice<
  "horizontal_list_section",
  HorizontalListSectionSliceVariation
>;

/**
 * Primary content in *Image → Primary*
 */
export interface ImageSliceDefaultPrimary {
  /**
   * Image field in *Image → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: image.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Image Caption field in *Image → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: image.primary.image_caption
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  image_caption: prismic.RichTextField;
}

/**
 * Default variation for Image Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ImageSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ImageSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *Image*
 */
type ImageSliceVariation = ImageSliceDefault;

/**
 * Image Shared Slice
 *
 * - **API ID**: `image`
 * - **Description**: Image
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ImageSlice = prismic.SharedSlice<"image", ImageSliceVariation>;

/**
 * Primary content in *ListWithSwitch → Primary*
 */
export interface ListWithSwitchSliceDefaultPrimary {
  /**
   * Description field in *ListWithSwitch → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: list_with_switch.primary.description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;

  /**
   * Title field in *ListWithSwitch → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: list_with_switch.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;
}

/**
 * Primary content in *ListWithSwitch → Items*
 */
export interface ListWithSwitchSliceDefaultItem {
  /**
   * Item field in *ListWithSwitch → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: list_with_switch.items[].item
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  item: prismic.KeyTextField;
}

/**
 * Default variation for ListWithSwitch Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ListWithSwitchSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ListWithSwitchSliceDefaultPrimary>,
  Simplify<ListWithSwitchSliceDefaultItem>
>;

/**
 * Slice variation for *ListWithSwitch*
 */
type ListWithSwitchSliceVariation = ListWithSwitchSliceDefault;

/**
 * ListWithSwitch Shared Slice
 *
 * - **API ID**: `list_with_switch`
 * - **Description**: ListWithSwitch
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ListWithSwitchSlice = prismic.SharedSlice<
  "list_with_switch",
  ListWithSwitchSliceVariation
>;

/**
 * Primary content in *LocateUsSection → Primary*
 */
export interface LocateUsSectionSliceDefaultPrimary {
  /**
   * Title field in *LocateUsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: locate_us_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *LocateUsSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: locate_us_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Default variation for LocateUsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type LocateUsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<LocateUsSectionSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *LocateUsSection*
 */
type LocateUsSectionSliceVariation = LocateUsSectionSliceDefault;

/**
 * LocateUsSection Shared Slice
 *
 * - **API ID**: `locate_us_section`
 * - **Description**: LocateUsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type LocateUsSectionSlice = prismic.SharedSlice<
  "locate_us_section",
  LocateUsSectionSliceVariation
>;

/**
 * Primary content in *LondonPlumbingAreasHeroSlice → Primary*
 */
export interface LondonPlumbingAreasSliceDefaultPrimary {
  /**
   * title field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * subtitle field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;

  /**
   * field_label field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.field_label
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  field_label: prismic.KeyTextField;

  /**
   * filed_placeholder field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.filed_placeholder
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  filed_placeholder: prismic.RichTextField;

  /**
   * mobile-title field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.mobileTitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  mobileTitle: prismic.RichTextField;

  /**
   * mobileSubtitle field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.mobileSubtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  mobileSubtitle: prismic.RichTextField;

  /**
   * mobileDescription field in *LondonPlumbingAreasHeroSlice → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.primary.mobileDescription
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  mobileDescription: prismic.RichTextField;
}

/**
 * Primary content in *LondonPlumbingAreasHeroSlice → Items*
 */
export interface LondonPlumbingAreasSliceDefaultItem {
  /**
   * paragraph field in *LondonPlumbingAreasHeroSlice → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: london_plumbing_areas.items[].pargraph
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  pargraph: prismic.RichTextField;
}

/**
 * Default variation for LondonPlumbingAreasHeroSlice Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type LondonPlumbingAreasSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<LondonPlumbingAreasSliceDefaultPrimary>,
  Simplify<LondonPlumbingAreasSliceDefaultItem>
>;

/**
 * Slice variation for *LondonPlumbingAreasHeroSlice*
 */
type LondonPlumbingAreasSliceVariation = LondonPlumbingAreasSliceDefault;

/**
 * LondonPlumbingAreasHeroSlice Shared Slice
 *
 * - **API ID**: `london_plumbing_areas`
 * - **Description**: LondonPlumbingAreas
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type LondonPlumbingAreasSlice = prismic.SharedSlice<
  "london_plumbing_areas",
  LondonPlumbingAreasSliceVariation
>;

/**
 * Default variation for MapSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type MapSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Record<string, never>,
  never
>;

/**
 * Slice variation for *MapSection*
 */
type MapSectionSliceVariation = MapSectionSliceDefault;

/**
 * MapSection Shared Slice
 *
 * - **API ID**: `map_section`
 * - **Description**: MapSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type MapSectionSlice = prismic.SharedSlice<
  "map_section",
  MapSectionSliceVariation
>;

/**
 * Primary content in *MarqueeSection → Primary*
 */
export interface MarqueeSectionSliceDefaultPrimary {
  /**
   * Text divider field in *MarqueeSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: marquee_section.primary.text_divider
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  text_divider: prismic.ImageField<never>;

  /**
   * Title field in *MarqueeSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: marquee_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;
}

/**
 * Default variation for MarqueeSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type MarqueeSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<MarqueeSectionSliceDefaultPrimary>,
  never
>;

/**
 * Primary content in *MarqueeSection → Primary*
 */
export interface MarqueeSectionSliceDarkPrimary {
  /**
   * Text divider field in *MarqueeSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: marquee_section.primary.text_divider
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  text_divider: prismic.ImageField<never>;

  /**
   * Title field in *MarqueeSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: marquee_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;
}

/**
 * dark variation for MarqueeSection Slice
 *
 * - **API ID**: `dark`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type MarqueeSectionSliceDark = prismic.SharedSliceVariation<
  "dark",
  Simplify<MarqueeSectionSliceDarkPrimary>,
  never
>;

/**
 * Slice variation for *MarqueeSection*
 */
type MarqueeSectionSliceVariation =
  | MarqueeSectionSliceDefault
  | MarqueeSectionSliceDark;

/**
 * MarqueeSection Shared Slice
 *
 * - **API ID**: `marquee_section`
 * - **Description**: MarqueeSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type MarqueeSectionSlice = prismic.SharedSlice<
  "marquee_section",
  MarqueeSectionSliceVariation
>;

/**
 * Primary content in *Paragraph → Primary*
 */
export interface ParagraphSliceDefaultPrimary {
  /**
   * Content field in *Paragraph → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: paragraph.primary.content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  content: prismic.RichTextField;
}

/**
 * Default variation for Paragraph Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ParagraphSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ParagraphSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *Paragraph*
 */
type ParagraphSliceVariation = ParagraphSliceDefault;

/**
 * Paragraph Shared Slice
 *
 * - **API ID**: `paragraph`
 * - **Description**: Paragraph
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ParagraphSlice = prismic.SharedSlice<
  "paragraph",
  ParagraphSliceVariation
>;

/**
 * Primary content in *PartnersSection → Primary*
 */
export interface PartnersSectionSliceDefaultPrimary {
  /**
   * Title field in *PartnersSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: partners_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *PartnersSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: partners_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *PartnersSection → Items*
 */
export interface PartnersSectionSliceDefaultItem {
  /**
   * Partner Logo field in *PartnersSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: partners_section.items[].partner_logo
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  partner_logo: prismic.ImageField<never>;
}

/**
 * Default variation for PartnersSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PartnersSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PartnersSectionSliceDefaultPrimary>,
  Simplify<PartnersSectionSliceDefaultItem>
>;

/**
 * Slice variation for *PartnersSection*
 */
type PartnersSectionSliceVariation = PartnersSectionSliceDefault;

/**
 * PartnersSection Shared Slice
 *
 * - **API ID**: `partners_section`
 * - **Description**: PartnersSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PartnersSectionSlice = prismic.SharedSlice<
  "partners_section",
  PartnersSectionSliceVariation
>;

/**
 * Primary content in *PeacePlanAdvantages → Primary*
 */
export interface PeacePlanAdvantagesSliceDefaultPrimary {
  /**
   * Main title field in *PeacePlanAdvantages → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_advantages.primary.main_title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  main_title: prismic.RichTextField;

  /**
   * Image field in *PeacePlanAdvantages → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_advantages.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *PeacePlanAdvantages → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_advantages.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *PeacePlanAdvantages → Items*
 */
export interface PeacePlanAdvantagesSliceDefaultItem {
  /**
   * Advantage icon field in *PeacePlanAdvantages → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_advantages.items[].advantage_icon
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  advantage_icon: prismic.ImageField<never>;

  /**
   * Advantage title field in *PeacePlanAdvantages → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_advantages.items[].advantage_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  advantage_title: prismic.KeyTextField;

  /**
   * Advantage description field in *PeacePlanAdvantages → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_advantages.items[].advantage_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  advantage_description: prismic.RichTextField;
}

/**
 * Default variation for PeacePlanAdvantages Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanAdvantagesSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PeacePlanAdvantagesSliceDefaultPrimary>,
  Simplify<PeacePlanAdvantagesSliceDefaultItem>
>;

/**
 * Slice variation for *PeacePlanAdvantages*
 */
type PeacePlanAdvantagesSliceVariation = PeacePlanAdvantagesSliceDefault;

/**
 * PeacePlanAdvantages Shared Slice
 *
 * - **API ID**: `peace_plan_advantages`
 * - **Description**: PeacePlanAdvantages
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanAdvantagesSlice = prismic.SharedSlice<
  "peace_plan_advantages",
  PeacePlanAdvantagesSliceVariation
>;

/**
 * Primary content in *PeacePlanCta → Primary*
 */
export interface PeacePlanCtaSliceDefaultPrimary {
  /**
   * Title field in *PeacePlanCta → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_cta.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *PeacePlanCta → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_cta.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Image field in *PeacePlanCta → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_cta.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *PeacePlanCta → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_cta.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Default variation for PeacePlanCta Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanCtaSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PeacePlanCtaSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *PeacePlanCta*
 */
type PeacePlanCtaSliceVariation = PeacePlanCtaSliceDefault;

/**
 * PeacePlanCta Shared Slice
 *
 * - **API ID**: `peace_plan_cta`
 * - **Description**: PeacePlanCta
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanCtaSlice = prismic.SharedSlice<
  "peace_plan_cta",
  PeacePlanCtaSliceVariation
>;

/**
 * Primary content in *PeacePlanDescriptionWithToggler → Primary*
 */
export interface PeacePlanDescriptionWithTogglerSliceDefaultPrimary {
  /**
   * Title field in *PeacePlanDescriptionWithToggler → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_description_with_toggler.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *PeacePlanDescriptionWithToggler → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_description_with_toggler.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Left image field in *PeacePlanDescriptionWithToggler → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_description_with_toggler.primary.left_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  left_image: prismic.ImageField<never>;

  /**
   * Right image field in *PeacePlanDescriptionWithToggler → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_description_with_toggler.primary.right_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  right_image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *PeacePlanDescriptionWithToggler → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_description_with_toggler.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Default variation for PeacePlanDescriptionWithToggler Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanDescriptionWithTogglerSliceDefault =
  prismic.SharedSliceVariation<
    "default",
    Simplify<PeacePlanDescriptionWithTogglerSliceDefaultPrimary>,
    never
  >;

/**
 * Slice variation for *PeacePlanDescriptionWithToggler*
 */
type PeacePlanDescriptionWithTogglerSliceVariation =
  PeacePlanDescriptionWithTogglerSliceDefault;

/**
 * PeacePlanDescriptionWithToggler Shared Slice
 *
 * - **API ID**: `peace_plan_description_with_toggler`
 * - **Description**: PeacePlanDescriptionWithToggler
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanDescriptionWithTogglerSlice = prismic.SharedSlice<
  "peace_plan_description_with_toggler",
  PeacePlanDescriptionWithTogglerSliceVariation
>;

/**
 * Primary content in *PeacePlanSubscriptionBenefits → Primary*
 */
export interface PeacePlanSubscriptionBenefitsSliceDefaultPrimary {
  /**
   * Price per field in *PeacePlanSubscriptionBenefits → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_subscription_benefits.primary.price_per
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  price_per: prismic.KeyTextField;

  /**
   * Title field in *PeacePlanSubscriptionBenefits → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_subscription_benefits.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Total price field in *PeacePlanSubscriptionBenefits → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_subscription_benefits.primary.total_price
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  total_price: prismic.KeyTextField;

  /**
   * Header anchor name field in *PeacePlanSubscriptionBenefits → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_subscription_benefits.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *PeacePlanSubscriptionBenefits → Items*
 */
export interface PeacePlanSubscriptionBenefitsSliceDefaultItem {
  /**
   * List item field in *PeacePlanSubscriptionBenefits → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: peace_plan_subscription_benefits.items[].list_item
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  list_item: prismic.KeyTextField;

  /**
   * Included field in *PeacePlanSubscriptionBenefits → Items*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: peace_plan_subscription_benefits.items[].included
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  included: prismic.BooleanField;
}

/**
 * Default variation for PeacePlanSubscriptionBenefits Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanSubscriptionBenefitsSliceDefault =
  prismic.SharedSliceVariation<
    "default",
    Simplify<PeacePlanSubscriptionBenefitsSliceDefaultPrimary>,
    Simplify<PeacePlanSubscriptionBenefitsSliceDefaultItem>
  >;

/**
 * Slice variation for *PeacePlanSubscriptionBenefits*
 */
type PeacePlanSubscriptionBenefitsSliceVariation =
  PeacePlanSubscriptionBenefitsSliceDefault;

/**
 * PeacePlanSubscriptionBenefits Shared Slice
 *
 * - **API ID**: `peace_plan_subscription_benefits`
 * - **Description**: PeacePlanSubscriptionBenefits
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PeacePlanSubscriptionBenefitsSlice = prismic.SharedSlice<
  "peace_plan_subscription_benefits",
  PeacePlanSubscriptionBenefitsSliceVariation
>;

/**
 * Primary content in *PhoneAnimationSection → Primary*
 */
export interface PhoneAnimationSectionSliceDefaultPrimary {
  /**
   * Title field in *PhoneAnimationSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: phone_animation_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *PhoneAnimationSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: phone_animation_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;

  /**
   * Video in phone field in *PhoneAnimationSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: phone_animation_section.primary.video_in_phone
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  video_in_phone: prismic.KeyTextField;
}

/**
 * Primary content in *PhoneAnimationSection → Items*
 */
export interface PhoneAnimationSectionSliceDefaultItem {
  /**
   * Description field in *PhoneAnimationSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: phone_animation_section.items[].description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Icon field in *PhoneAnimationSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: phone_animation_section.items[].icon
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  icon: prismic.ImageField<never>;
}

/**
 * Default variation for PhoneAnimationSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PhoneAnimationSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PhoneAnimationSectionSliceDefaultPrimary>,
  Simplify<PhoneAnimationSectionSliceDefaultItem>
>;

/**
 * Slice variation for *PhoneAnimationSection*
 */
type PhoneAnimationSectionSliceVariation = PhoneAnimationSectionSliceDefault;

/**
 * PhoneAnimationSection Shared Slice
 *
 * - **API ID**: `phone_animation_section`
 * - **Description**: PhoneAnimationSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PhoneAnimationSectionSlice = prismic.SharedSlice<
  "phone_animation_section",
  PhoneAnimationSectionSliceVariation
>;

/**
 * Primary content in *PlansSection → Primary*
 */
export interface PlansSectionSliceDefaultPrimary {
  /**
   * Title field in *PlansSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *PlansSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *PlansSection → Items*
 */
export interface PlansSectionSliceDefaultItem {
  /**
   * Plan Name field in *PlansSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.items[].plan_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  plan_name: prismic.KeyTextField;

  /**
   * Plan Price field in *PlansSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.items[].plan_price
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  plan_price: prismic.RichTextField;

  /**
   * Plan Benefits field in *PlansSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.items[].plan_benefits
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  plan_benefits: prismic.KeyTextField;

  /**
   * Plan Link field in *PlansSection → Items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.items[].plan_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  plan_link: prismic.LinkField;

  /**
   * Plan Link Text field in *PlansSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plans_section.items[].plan_link_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  plan_link_text: prismic.KeyTextField;
}

/**
 * Default variation for PlansSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PlansSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PlansSectionSliceDefaultPrimary>,
  Simplify<PlansSectionSliceDefaultItem>
>;

/**
 * Slice variation for *PlansSection*
 */
type PlansSectionSliceVariation = PlansSectionSliceDefault;

/**
 * PlansSection Shared Slice
 *
 * - **API ID**: `plans_section`
 * - **Description**: PlansSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PlansSectionSlice = prismic.SharedSlice<
  "plans_section",
  PlansSectionSliceVariation
>;

/**
 * Primary content in *PlumbingServicesSection → Primary*
 */
export interface PlumbingServicesSectionSliceDefaultPrimary {
  /**
   * Image field in *PlumbingServicesSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: plumbing_services_section.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *PlumbingServicesSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plumbing_services_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *PlumbingServicesSection → Items*
 */
export interface PlumbingServicesSectionSliceDefaultItem {
  /**
   * Left list field in *PlumbingServicesSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plumbing_services_section.items[].left_list
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  left_list: prismic.RichTextField;

  /**
   * Right list field in *PlumbingServicesSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: plumbing_services_section.items[].right_list
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  right_list: prismic.RichTextField;
}

/**
 * Default variation for PlumbingServicesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PlumbingServicesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PlumbingServicesSectionSliceDefaultPrimary>,
  Simplify<PlumbingServicesSectionSliceDefaultItem>
>;

/**
 * Slice variation for *PlumbingServicesSection*
 */
type PlumbingServicesSectionSliceVariation =
  PlumbingServicesSectionSliceDefault;

/**
 * PlumbingServicesSection Shared Slice
 *
 * - **API ID**: `plumbing_services_section`
 * - **Description**: PlumbingServicesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PlumbingServicesSectionSlice = prismic.SharedSlice<
  "plumbing_services_section",
  PlumbingServicesSectionSliceVariation
>;

/**
 * Primary content in *PreviewSection → Primary*
 */
export interface PreviewSectionSliceDefaultPrimary {
  /**
   * Title field in *PreviewSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Subtitle field in *PreviewSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;
}

/**
 * Primary content in *PreviewSection → Items*
 */
export interface PreviewSectionSliceDefaultItem {
  /**
   * Preview Image field in *PreviewSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].preview_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview_image: prismic.ImageField<"tablet">;

  /**
   * Link For Learn More field in *PreviewSection → Items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].link_for_learn_more
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  link_for_learn_more: prismic.LinkField;
}

/**
 * Default variation for PreviewSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PreviewSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<PreviewSectionSliceDefaultPrimary>,
  Simplify<PreviewSectionSliceDefaultItem>
>;

/**
 * Primary content in *PreviewSection → Primary*
 */
export interface PreviewSectionSliceHeroPrimary {
  /**
   * Title field in *PreviewSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Subtitle field in *PreviewSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;

  /**
   * Action Text field in *PreviewSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.action_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  action_text: prismic.KeyTextField;

  /**
   * Action Link field in *PreviewSection → Primary*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.action_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  action_link: prismic.LinkField;
}

/**
 * Primary content in *PreviewSection → Items*
 */
export interface PreviewSectionSliceHeroItem {
  /**
   * Preview Image field in *PreviewSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].preview_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview_image: prismic.ImageField<"tablet">;
}

/**
 * Hero variation for PreviewSection Slice
 *
 * - **API ID**: `hero`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PreviewSectionSliceHero = prismic.SharedSliceVariation<
  "hero",
  Simplify<PreviewSectionSliceHeroPrimary>,
  Simplify<PreviewSectionSliceHeroItem>
>;

/**
 * Primary content in *PreviewSection → Primary*
 */
export interface PreviewSectionSliceWithoutReferringPrimary {
  /**
   * Title field in *PreviewSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Subtitle field in *PreviewSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.primary.subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;
}

/**
 * Primary content in *PreviewSection → Items*
 */
export interface PreviewSectionSliceWithoutReferringItem {
  /**
   * Preview Image field in *PreviewSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].preview_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview_image: prismic.ImageField<"tablet">;
}

/**
 * Without Referring variation for PreviewSection Slice
 *
 * - **API ID**: `withoutReferring`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PreviewSectionSliceWithoutReferring = prismic.SharedSliceVariation<
  "withoutReferring",
  Simplify<PreviewSectionSliceWithoutReferringPrimary>,
  Simplify<PreviewSectionSliceWithoutReferringItem>
>;

/**
 * Primary content in *PreviewSection → Items*
 */
export interface PreviewSectionSliceSliderItem {
  /**
   * Title field in *PreviewSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Subtitle field in *PreviewSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].subtitle
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  subtitle: prismic.RichTextField;

  /**
   * Preview Image field in *PreviewSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].preview_image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  preview_image: prismic.ImageField<"tablet">;

  /**
   * Preview In Full Screen field in *PreviewSection → Items*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: preview_section.items[].preview_in_full_screen
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  preview_in_full_screen: prismic.BooleanField;

  /**
   * Action Text field in *PreviewSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].action_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  action_text: prismic.KeyTextField;

  /**
   * Action Link field in *PreviewSection → Items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: preview_section.items[].action_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  action_link: prismic.LinkField;
}

/**
 * Slider variation for PreviewSection Slice
 *
 * - **API ID**: `slider`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PreviewSectionSliceSlider = prismic.SharedSliceVariation<
  "slider",
  Record<string, never>,
  Simplify<PreviewSectionSliceSliderItem>
>;

/**
 * Slice variation for *PreviewSection*
 */
type PreviewSectionSliceVariation =
  | PreviewSectionSliceDefault
  | PreviewSectionSliceHero
  | PreviewSectionSliceWithoutReferring
  | PreviewSectionSliceSlider;

/**
 * PreviewSection Shared Slice
 *
 * - **API ID**: `preview_section`
 * - **Description**: PreviewSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type PreviewSectionSlice = prismic.SharedSlice<
  "preview_section",
  PreviewSectionSliceVariation
>;

/**
 * Primary content in *ProtectingHouseholds → Primary*
 */
export interface ProtectingHouseholdsSliceDefaultPrimary {
  /**
   * Title field in *ProtectingHouseholds → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: Your Trusted Plumbing & Heating Partner in London
   * - **API ID Path**: protecting_households.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *ProtectingHouseholds → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: We serve all the major London Boroughs
   * - **API ID Path**: protecting_households.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * London Image field in *ProtectingHouseholds → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *ProtectingHouseholds → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: london-coverage
   * - **API ID Path**: protecting_households.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *ProtectingHouseholds → Items*
 */
export interface ProtectingHouseholdsSliceDefaultItem {
  /**
   * Borough Name field in *ProtectingHouseholds → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: Westminster
   * - **API ID Path**: protecting_households.items[].borough
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  borough: prismic.KeyTextField;
}

/**
 * Default variation for ProtectingHouseholds Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default variation
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ProtectingHouseholdsSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ProtectingHouseholdsSliceDefaultPrimary>,
  Simplify<ProtectingHouseholdsSliceDefaultItem>
>;

/**
 * Slice variation for *ProtectingHouseholds*
 */
type ProtectingHouseholdsSliceVariation = ProtectingHouseholdsSliceDefault;

/**
 * ProtectingHouseholds Shared Slice
 *
 * - **API ID**: `protecting_households`
 * - **Description**: A section showing London boroughs coverage
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ProtectingHouseholdsSlice = prismic.SharedSlice<
  "protecting_households",
  ProtectingHouseholdsSliceVariation
>;

/**
 * Primary content in *ProtectingHouseholdsSection → Primary*
 */
export interface ProtectingHouseholdsSectionSliceDefaultPrimary {
  /**
   * title_1 field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.title_1
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_1: prismic.RichTextField;

  /**
   * title_2 field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.title_2
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_2: prismic.RichTextField;

  /**
   * title_3 field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.title_3
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_3: prismic.RichTextField;

  /**
   * description field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * description_2 field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.description_2
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description_2: prismic.RichTextField;

  /**
   * description_3 field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.description_3
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description_3: prismic.RichTextField;

  /**
   * description_4 field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.description_4
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description_4: prismic.RichTextField;

  /**
   * sectionImage field in *ProtectingHouseholdsSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.primary.sectionimage
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  sectionimage: prismic.ImageField<never>;
}

/**
 * Primary content in *ProtectingHouseholdsSection → Items*
 */
export interface ProtectingHouseholdsSectionSliceDefaultItem {
  /**
   * name field in *ProtectingHouseholdsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.items[].name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  name: prismic.KeyTextField;

  /**
   * lat field in *ProtectingHouseholdsSection → Items*
   *
   * - **Field Type**: Number
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.items[].lat
   * - **Documentation**: https://prismic.io/docs/field#number
   */
  lat: prismic.NumberField;

  /**
   * lng field in *ProtectingHouseholdsSection → Items*
   *
   * - **Field Type**: Number
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.items[].lng
   * - **Documentation**: https://prismic.io/docs/field#number
   */
  lng: prismic.NumberField;

  /**
   * postcode field in *ProtectingHouseholdsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: protecting_households_section.items[].postcode
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  postcode: prismic.KeyTextField;
}

/**
 * Default variation for ProtectingHouseholdsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ProtectingHouseholdsSectionSliceDefault =
  prismic.SharedSliceVariation<
    "default",
    Simplify<ProtectingHouseholdsSectionSliceDefaultPrimary>,
    Simplify<ProtectingHouseholdsSectionSliceDefaultItem>
  >;

/**
 * Slice variation for *ProtectingHouseholdsSection*
 */
type ProtectingHouseholdsSectionSliceVariation =
  ProtectingHouseholdsSectionSliceDefault;

/**
 * ProtectingHouseholdsSection Shared Slice
 *
 * - **API ID**: `protecting_households_section`
 * - **Description**: ProtectingHouseholdsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ProtectingHouseholdsSectionSlice = prismic.SharedSlice<
  "protecting_households_section",
  ProtectingHouseholdsSectionSliceVariation
>;

/**
 * Primary content in *RecentWork → Primary*
 */
export interface RecentWorkSliceDefaultPrimary {
  /**
   * title_1 field in *RecentWork → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.primary.title_1
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_1: prismic.RichTextField;

  /**
   * title_2 field in *RecentWork → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.primary.title_2
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_2: prismic.RichTextField;

  /**
   * title_3 field in *RecentWork → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.primary.title_3
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_3: prismic.RichTextField;
}

/**
 * Primary content in *RecentWork → Items*
 */
export interface RecentWorkSliceDefaultItem {
  /**
   * id field in *RecentWork → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.items[].image_id
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  image_id: prismic.KeyTextField;

  /**
   * name field in *RecentWork → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.items[].name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  name: prismic.KeyTextField;

  /**
   * location field in *RecentWork → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.items[].location
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  location: prismic.KeyTextField;

  /**
   * image field in *RecentWork → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: recent_work.items[].image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;
}

/**
 * Default variation for RecentWork Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type RecentWorkSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<RecentWorkSliceDefaultPrimary>,
  Simplify<RecentWorkSliceDefaultItem>
>;

/**
 * Slice variation for *RecentWork*
 */
type RecentWorkSliceVariation = RecentWorkSliceDefault;

/**
 * RecentWork Shared Slice
 *
 * - **API ID**: `recent_work`
 * - **Description**: RecentWork
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type RecentWorkSlice = prismic.SharedSlice<
  "recent_work",
  RecentWorkSliceVariation
>;

/**
 * Primary content in *ReferAnEngineerSection → Primary*
 */
export interface ReferAnEngineerSectionSliceDefaultPrimary {
  /**
   * title_1 field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.title_1
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_1: prismic.RichTextField;

  /**
   * title_2 field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.title_2
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_2: prismic.RichTextField;

  /**
   * title_3 field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.title_3
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title_3: prismic.RichTextField;

  /**
   * paragraph_1 field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.paragraph_1
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  paragraph_1: prismic.RichTextField;

  /**
   * paragraph_2 field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.paragraph_2
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  paragraph_2: prismic.RichTextField;

  /**
   * buttonText field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.buttontext
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  buttontext: prismic.KeyTextField;

  /**
   * image field in *ReferAnEngineerSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_an_engineer_section.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;
}

/**
 * Default variation for ReferAnEngineerSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReferAnEngineerSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ReferAnEngineerSectionSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *ReferAnEngineerSection*
 */
type ReferAnEngineerSectionSliceVariation = ReferAnEngineerSectionSliceDefault;

/**
 * ReferAnEngineerSection Shared Slice
 *
 * - **API ID**: `refer_an_engineer_section`
 * - **Description**: ReferAnEngineerSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReferAnEngineerSectionSlice = prismic.SharedSlice<
  "refer_an_engineer_section",
  ReferAnEngineerSectionSliceVariation
>;

/**
 * Primary content in *ReferAndEarn → Primary*
 */
export interface ReferAndEarnSliceDefaultPrimary {
  /**
   * Title field in *ReferAndEarn → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_and_earn.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Image field in *ReferAndEarn → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_and_earn.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Description field in *ReferAndEarn → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_and_earn.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Button text field in *ReferAndEarn → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: refer_and_earn.primary.button_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  button_text: prismic.KeyTextField;
}

/**
 * Default variation for ReferAndEarn Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReferAndEarnSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ReferAndEarnSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *ReferAndEarn*
 */
type ReferAndEarnSliceVariation = ReferAndEarnSliceDefault;

/**
 * ReferAndEarn Shared Slice
 *
 * - **API ID**: `refer_and_earn`
 * - **Description**: ReferAndEarn
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReferAndEarnSlice = prismic.SharedSlice<
  "refer_and_earn",
  ReferAndEarnSliceVariation
>;

/**
 * Primary content in *ReferralBonusSection → Primary*
 */
export interface ReferralBonusSectionSliceDefaultPrimary {
  /**
   * leftBarTitlePart_1 field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.leftbartitlepart_1
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  leftbartitlepart_1: prismic.RichTextField;

  /**
   * leftBarTitlePart_2 field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.leftbartitlepart_2
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  leftbartitlepart_2: prismic.KeyTextField;

  /**
   * description field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;

  /**
   * image field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * description_2 field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.description_2
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_2: prismic.KeyTextField;

  /**
   * listTitle field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.listtitle
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  listtitle: prismic.KeyTextField;

  /**
   * description_3 field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.description_3
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_3: prismic.KeyTextField;

  /**
   * description_4 field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.description_4
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description_4: prismic.KeyTextField;

  /**
   * ShareLinkTitle field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.sharelinktitle
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  sharelinktitle: prismic.KeyTextField;

  /**
   * shareLinkDescription field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.sharelinkdescription
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  sharelinkdescription: prismic.KeyTextField;

  /**
   * buttonText field in *ReferralBonusSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.primary.buttontext
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  buttontext: prismic.KeyTextField;
}

/**
 * Primary content in *ReferralBonusSection → Items*
 */
export interface ReferralBonusSectionSliceDefaultItem {
  /**
   * price field in *ReferralBonusSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.items[].price
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  price: prismic.KeyTextField;

  /**
   * description field in *ReferralBonusSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: referral_bonus_section.items[].description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;
}

/**
 * Default variation for ReferralBonusSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReferralBonusSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ReferralBonusSectionSliceDefaultPrimary>,
  Simplify<ReferralBonusSectionSliceDefaultItem>
>;

/**
 * Slice variation for *ReferralBonusSection*
 */
type ReferralBonusSectionSliceVariation = ReferralBonusSectionSliceDefault;

/**
 * ReferralBonusSection Shared Slice
 *
 * - **API ID**: `referral_bonus_section`
 * - **Description**: ReferralBonusSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReferralBonusSectionSlice = prismic.SharedSlice<
  "referral_bonus_section",
  ReferralBonusSectionSliceVariation
>;

/**
 * Primary content in *ReviewsSection → Primary*
 */
export interface ReviewsSectionSliceDefaultPrimary {
  /**
   * Title field in *ReviewsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *ReviewsSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *ReviewsSection → Items*
 */
export interface ReviewsSectionSliceDefaultItem {
  /**
   * Review Title field in *ReviewsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  review_title: prismic.KeyTextField;

  /**
   * Review Content field in *ReviewsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_content
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  review_content: prismic.KeyTextField;

  /**
   * Review Author field in *ReviewsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_author
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  review_author: prismic.KeyTextField;

  /**
   * Review Date field in *ReviewsSection → Items*
   *
   * - **Field Type**: Date
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_date
   * - **Documentation**: https://prismic.io/docs/field#date
   */
  review_date: prismic.DateField;

  /**
   * Review Rating field in *ReviewsSection → Items*
   *
   * - **Field Type**: Number
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_rating
   * - **Documentation**: https://prismic.io/docs/field#number
   */
  review_rating: prismic.NumberField;
}

/**
 * Default variation for ReviewsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReviewsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ReviewsSectionSliceDefaultPrimary>,
  Simplify<ReviewsSectionSliceDefaultItem>
>;

/**
 * Primary content in *ReviewsSection → Primary*
 */
export interface ReviewsSectionSliceDarkBackgroundPrimary {
  /**
   * Title field in *ReviewsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *ReviewsSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *ReviewsSection → Items*
 */
export interface ReviewsSectionSliceDarkBackgroundItem {
  /**
   * Review Title field in *ReviewsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  review_title: prismic.KeyTextField;

  /**
   * Review Content field in *ReviewsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_content
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  review_content: prismic.KeyTextField;

  /**
   * Review Author field in *ReviewsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_author
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  review_author: prismic.KeyTextField;

  /**
   * Review Date field in *ReviewsSection → Items*
   *
   * - **Field Type**: Date
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_date
   * - **Documentation**: https://prismic.io/docs/field#date
   */
  review_date: prismic.DateField;

  /**
   * Review Rating field in *ReviewsSection → Items*
   *
   * - **Field Type**: Number
   * - **Placeholder**: *None*
   * - **API ID Path**: reviews_section.items[].review_rating
   * - **Documentation**: https://prismic.io/docs/field#number
   */
  review_rating: prismic.NumberField;
}

/**
 * Dark Background variation for ReviewsSection Slice
 *
 * - **API ID**: `darkBackground`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReviewsSectionSliceDarkBackground = prismic.SharedSliceVariation<
  "darkBackground",
  Simplify<ReviewsSectionSliceDarkBackgroundPrimary>,
  Simplify<ReviewsSectionSliceDarkBackgroundItem>
>;

/**
 * Slice variation for *ReviewsSection*
 */
type ReviewsSectionSliceVariation =
  | ReviewsSectionSliceDefault
  | ReviewsSectionSliceDarkBackground;

/**
 * ReviewsSection Shared Slice
 *
 * - **API ID**: `reviews_section`
 * - **Description**: ReviewsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ReviewsSectionSlice = prismic.SharedSlice<
  "reviews_section",
  ReviewsSectionSliceVariation
>;

/**
 * Primary content in *ServicesSection → Items*
 */
export interface ServicesSectionSliceDefaultItem {
  /**
   * id field in *ServicesSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: services_section.items[].service_id
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  service_id: prismic.KeyTextField;

  /**
   * title field in *ServicesSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: services_section.items[].title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title: prismic.KeyTextField;

  /**
   * description field in *ServicesSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: services_section.items[].description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;

  /**
   * buttonText field in *ServicesSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: services_section.items[].buttontext
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  buttontext: prismic.KeyTextField;

  /**
   * buttonLink field in *ServicesSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: services_section.items[].buttonlink
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  buttonlink: prismic.KeyTextField;
}

/**
 * Default variation for ServicesSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ServicesSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Record<string, never>,
  Simplify<ServicesSectionSliceDefaultItem>
>;

/**
 * Slice variation for *ServicesSection*
 */
type ServicesSectionSliceVariation = ServicesSectionSliceDefault;

/**
 * ServicesSection Shared Slice
 *
 * - **API ID**: `services_section`
 * - **Description**: ServicesSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ServicesSectionSlice = prismic.SharedSlice<
  "services_section",
  ServicesSectionSliceVariation
>;

/**
 * Primary content in *ShuffledCardsList → Primary*
 */
export interface ShuffledCardsListSliceDefaultPrimary {
  /**
   * Title field in *ShuffledCardsList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *ShuffledCardsList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Moving text divider field in *ShuffledCardsList → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.primary.moving_text_divider
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  moving_text_divider: prismic.ImageField<never>;

  /**
   * Header anchor name field in *ShuffledCardsList → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *ShuffledCardsList → Items*
 */
export interface ShuffledCardsListSliceDefaultItem {
  /**
   * Title field in *ShuffledCardsList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.items[].title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title: prismic.KeyTextField;

  /**
   * Description field in *ShuffledCardsList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.items[].description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;

  /**
   * Type field in *ShuffledCardsList → Items*
   *
   * - **Field Type**: Select
   * - **Placeholder**: *None*
   * - **Default Value**: white
   * - **API ID Path**: shuffled_cards_list.items[].type
   * - **Documentation**: https://prismic.io/docs/field#select
   */
  type: prismic.SelectField<"white" | "darkGreen" | "green", "filled">;
}

/**
 * Default variation for ShuffledCardsList Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ShuffledCardsListSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<ShuffledCardsListSliceDefaultPrimary>,
  Simplify<ShuffledCardsListSliceDefaultItem>
>;

/**
 * Primary content in *ShuffledCardsList → Primary*
 */
export interface ShuffledCardsListSliceWithoutMarqueePrimary {
  /**
   * Title field in *ShuffledCardsList → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Header anchor name field in *ShuffledCardsList → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *ShuffledCardsList → Items*
 */
export interface ShuffledCardsListSliceWithoutMarqueeItem {
  /**
   * Type field in *ShuffledCardsList → Items*
   *
   * - **Field Type**: Select
   * - **Placeholder**: *None*
   * - **Default Value**: white
   * - **API ID Path**: shuffled_cards_list.items[].type
   * - **Documentation**: https://prismic.io/docs/field#select
   */
  type: prismic.SelectField<"white" | "darkGreen" | "green", "filled">;

  /**
   * Title field in *ShuffledCardsList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.items[].title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title: prismic.KeyTextField;

  /**
   * Description field in *ShuffledCardsList → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: shuffled_cards_list.items[].description
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  description: prismic.KeyTextField;
}

/**
 * Without marquee variation for ShuffledCardsList Slice
 *
 * - **API ID**: `withoutMarquee`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ShuffledCardsListSliceWithoutMarquee = prismic.SharedSliceVariation<
  "withoutMarquee",
  Simplify<ShuffledCardsListSliceWithoutMarqueePrimary>,
  Simplify<ShuffledCardsListSliceWithoutMarqueeItem>
>;

/**
 * Slice variation for *ShuffledCardsList*
 */
type ShuffledCardsListSliceVariation =
  | ShuffledCardsListSliceDefault
  | ShuffledCardsListSliceWithoutMarquee;

/**
 * ShuffledCardsList Shared Slice
 *
 * - **API ID**: `shuffled_cards_list`
 * - **Description**: ShuffledCardsList
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type ShuffledCardsListSlice = prismic.SharedSlice<
  "shuffled_cards_list",
  ShuffledCardsListSliceVariation
>;

/**
 * Primary content in *StepsSection → Primary*
 */
export interface StepsSectionSliceDefaultPrimary {
  /**
   * Title field in *StepsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: steps_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *StepsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: steps_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *StepsSection → Items*
 */
export interface StepsSectionSliceDefaultItem {
  /**
   * Step Title field in *StepsSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: steps_section.items[].step_title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  step_title: prismic.RichTextField;

  /**
   * Step Content field in *StepsSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: steps_section.items[].step_content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  step_content: prismic.RichTextField;
}

/**
 * Default variation for StepsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type StepsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<StepsSectionSliceDefaultPrimary>,
  Simplify<StepsSectionSliceDefaultItem>
>;

/**
 * Slice variation for *StepsSection*
 */
type StepsSectionSliceVariation = StepsSectionSliceDefault;

/**
 * StepsSection Shared Slice
 *
 * - **API ID**: `steps_section`
 * - **Description**: StepsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type StepsSectionSlice = prismic.SharedSlice<
  "steps_section",
  StepsSectionSliceVariation
>;

/**
 * Primary content in *SubscribtionsSection → Primary*
 */
export interface SubscribtionsSectionSliceDefaultPrimary {
  /**
   * Title field in *SubscribtionsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *SubscribtionsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;
}

/**
 * Primary content in *SubscribtionsSection → Items*
 */
export interface SubscribtionsSectionSliceDefaultItem {
  /**
   * Plan Title field in *SubscribtionsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.items[].plan_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  plan_title: prismic.KeyTextField;

  /**
   * Plan Price field in *SubscribtionsSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.items[].plan_price
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  plan_price: prismic.RichTextField;

  /**
   * Plan Benefits field in *SubscribtionsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.items[].plan_benefits
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  plan_benefits: prismic.KeyTextField;

  /**
   * Plan Link Text field in *SubscribtionsSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.items[].plan_link_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  plan_link_text: prismic.KeyTextField;

  /**
   * Plan Link field in *SubscribtionsSection → Items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: subscribtions_section.items[].plan_link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  plan_link: prismic.LinkField;
}

/**
 * Default variation for SubscribtionsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type SubscribtionsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<SubscribtionsSectionSliceDefaultPrimary>,
  Simplify<SubscribtionsSectionSliceDefaultItem>
>;

/**
 * Slice variation for *SubscribtionsSection*
 */
type SubscribtionsSectionSliceVariation = SubscribtionsSectionSliceDefault;

/**
 * SubscribtionsSection Shared Slice
 *
 * - **API ID**: `subscribtions_section`
 * - **Description**: SubscribtionsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type SubscribtionsSectionSlice = prismic.SharedSlice<
  "subscribtions_section",
  SubscribtionsSectionSliceVariation
>;

/**
 * Primary content in *TextAndImageSection → Primary*
 */
export interface TextAndImageSectionSliceDefaultPrimary {
  /**
   * Mirrored field in *TextAndImageSection → Primary*
   *
   * - **Field Type**: Boolean
   * - **Placeholder**: *None*
   * - **Default Value**: false
   * - **API ID Path**: text_and_image_section.primary.mirrored
   * - **Documentation**: https://prismic.io/docs/field#boolean
   */
  mirrored: prismic.BooleanField;

  /**
   * Title field in *TextAndImageSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: text_and_image_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *TextAndImageSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: text_and_image_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Image field in *TextAndImageSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: text_and_image_section.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Link Text field in *TextAndImageSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: text_and_image_section.primary.link_text
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  link_text: prismic.KeyTextField;

  /**
   * Link Url field in *TextAndImageSection → Primary*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: text_and_image_section.primary.link_url
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  link_url: prismic.LinkField;
}

/**
 * Default variation for TextAndImageSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type TextAndImageSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<TextAndImageSectionSliceDefaultPrimary>,
  never
>;

/**
 * Slice variation for *TextAndImageSection*
 */
type TextAndImageSectionSliceVariation = TextAndImageSectionSliceDefault;

/**
 * TextAndImageSection Shared Slice
 *
 * - **API ID**: `text_and_image_section`
 * - **Description**: TextAndImageSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type TextAndImageSectionSlice = prismic.SharedSlice<
  "text_and_image_section",
  TextAndImageSectionSliceVariation
>;

/**
 * Primary content in *TopBrandsSection → Primary*
 */
export interface TopBrandsSectionSliceDefaultPrimary {
  /**
   * Title field in *TopBrandsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_brands_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *TopBrandsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_brands_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Second description field in *TopBrandsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_brands_section.primary.second_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  second_description: prismic.RichTextField;

  /**
   * Footer text field in *TopBrandsSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_brands_section.primary.footer_text
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  footer_text: prismic.RichTextField;

  /**
   * Header anchor name field in *TopBrandsSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_brands_section.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Primary content in *TopBrandsSection → Items*
 */
export interface TopBrandsSectionSliceDefaultItem {
  /**
   * Logo field in *TopBrandsSection → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: top_brands_section.items[].logo
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  logo: prismic.ImageField<never>;
}

/**
 * Default variation for TopBrandsSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type TopBrandsSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<TopBrandsSectionSliceDefaultPrimary>,
  Simplify<TopBrandsSectionSliceDefaultItem>
>;

/**
 * Slice variation for *TopBrandsSection*
 */
type TopBrandsSectionSliceVariation = TopBrandsSectionSliceDefault;

/**
 * TopBrandsSection Shared Slice
 *
 * - **API ID**: `top_brands_section`
 * - **Description**: TopBrandsSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type TopBrandsSectionSlice = prismic.SharedSlice<
  "top_brands_section",
  TopBrandsSectionSliceVariation
>;

/**
 * Primary content in *TopServices → Primary*
 */
export interface TopServicesSliceDefaultPrimary {
  /**
   * Title field in *TopServices → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_services.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;
}

/**
 * Primary content in *TopServices → Items*
 */
export interface TopServicesSliceDefaultItem {
  /**
   * Title field in *TopServices → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_services.items[].title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  title: prismic.KeyTextField;

  /**
   * Content field in *TopServices → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: top_services.items[].content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  content: prismic.RichTextField;

  /**
   * Link field in *TopServices → Items*
   *
   * - **Field Type**: Link
   * - **Placeholder**: *None*
   * - **API ID Path**: top_services.items[].link
   * - **Documentation**: https://prismic.io/docs/field#link-content-relationship
   */
  link: prismic.LinkField;

  /**
   * Icon field in *TopServices → Items*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: top_services.items[].icon
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  icon: prismic.ImageField<never>;
}

/**
 * Default variation for TopServices Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type TopServicesSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<TopServicesSliceDefaultPrimary>,
  Simplify<TopServicesSliceDefaultItem>
>;

/**
 * Slice variation for *TopServices*
 */
type TopServicesSliceVariation = TopServicesSliceDefault;

/**
 * TopServices Shared Slice
 *
 * - **API ID**: `top_services`
 * - **Description**: TopServices
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type TopServicesSlice = prismic.SharedSlice<
  "top_services",
  TopServicesSliceVariation
>;

/**
 * Primary content in *UpgrageYourChC → Primary*
 */
export interface UpgrageYourChCSliceDefaultPrimary {
  /**
   * Title field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Second description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.second_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  second_description: prismic.RichTextField;

  /**
   * Third description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.third_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  third_description: prismic.RichTextField;

  /**
   * Image field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Default variation for UpgrageYourChC Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type UpgrageYourChCSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<UpgrageYourChCSliceDefaultPrimary>,
  never
>;

/**
 * Primary content in *UpgrageYourChC → Primary*
 */
export interface UpgrageYourChCSliceSecondPrimary {
  /**
   * Title field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Second description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.second_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  second_description: prismic.RichTextField;

  /**
   * Third description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.third_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  third_description: prismic.RichTextField;

  /**
   * Image field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Second variation for UpgrageYourChC Slice
 *
 * - **API ID**: `second`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type UpgrageYourChCSliceSecond = prismic.SharedSliceVariation<
  "second",
  Simplify<UpgrageYourChCSliceSecondPrimary>,
  never
>;

/**
 * Primary content in *UpgrageYourChC → Primary*
 */
export interface UpgrageYourChCSliceThirdPrimary {
  /**
   * Title field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Second description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.second_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  second_description: prismic.RichTextField;

  /**
   * Third description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.third_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  third_description: prismic.RichTextField;

  /**
   * Image field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;
}

/**
 * Third variation for UpgrageYourChC Slice
 *
 * - **API ID**: `third`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type UpgrageYourChCSliceThird = prismic.SharedSliceVariation<
  "third",
  Simplify<UpgrageYourChCSliceThirdPrimary>,
  never
>;

/**
 * Primary content in *UpgrageYourChC → Primary*
 */
export interface UpgrageYourChCSliceFourthPrimary {
  /**
   * Title field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * Second description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.second_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  second_description: prismic.RichTextField;

  /**
   * Third description field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.third_description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  third_description: prismic.RichTextField;

  /**
   * Image field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.image
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  image: prismic.ImageField<never>;

  /**
   * Header anchor name field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.header_anchor_name
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  header_anchor_name: prismic.KeyTextField;

  /**
   * Move to anchor id field in *UpgrageYourChC → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: upgrage_your_ch_c.primary.move_to_anchor_id
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  move_to_anchor_id: prismic.KeyTextField;
}

/**
 * Fourth variation for UpgrageYourChC Slice
 *
 * - **API ID**: `fourth`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type UpgrageYourChCSliceFourth = prismic.SharedSliceVariation<
  "fourth",
  Simplify<UpgrageYourChCSliceFourthPrimary>,
  never
>;

/**
 * Slice variation for *UpgrageYourChC*
 */
type UpgrageYourChCSliceVariation =
  | UpgrageYourChCSliceDefault
  | UpgrageYourChCSliceSecond
  | UpgrageYourChCSliceThird
  | UpgrageYourChCSliceFourth;

/**
 * UpgrageYourChC Shared Slice
 *
 * - **API ID**: `upgrage_your_ch_c`
 * - **Description**: UpgrageYourChC
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type UpgrageYourChCSlice = prismic.SharedSlice<
  "upgrage_your_ch_c",
  UpgrageYourChCSliceVariation
>;

/**
 * Primary content in *VideoAskSection → Primary*
 */
export interface VideoAskSectionSliceDefaultPrimary {
  /**
   * Title field in *VideoAskSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: video_ask_section.primary.title
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  title: prismic.RichTextField;

  /**
   * Description field in *VideoAskSection → Primary*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: video_ask_section.primary.description
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  description: prismic.RichTextField;

  /**
   * VideoAsk Link field in *VideoAskSection → Primary*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: video_ask_section.primary.videoask_link
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  videoask_link: prismic.KeyTextField;

  /**
   * VideoAsk Preview Avatar field in *VideoAskSection → Primary*
   *
   * - **Field Type**: Image
   * - **Placeholder**: *None*
   * - **API ID Path**: video_ask_section.primary.videoask_avatar
   * - **Documentation**: https://prismic.io/docs/field#image
   */
  videoask_avatar: prismic.ImageField<never>;
}

/**
 * Primary content in *VideoAskSection → Items*
 */
export interface VideoAskSectionSliceDefaultItem {
  /**
   * Benefit Title field in *VideoAskSection → Items*
   *
   * - **Field Type**: Text
   * - **Placeholder**: *None*
   * - **API ID Path**: video_ask_section.items[].benefit_title
   * - **Documentation**: https://prismic.io/docs/field#key-text
   */
  benefit_title: prismic.KeyTextField;

  /**
   * Benefit Content field in *VideoAskSection → Items*
   *
   * - **Field Type**: Rich Text
   * - **Placeholder**: *None*
   * - **API ID Path**: video_ask_section.items[].benefit_content
   * - **Documentation**: https://prismic.io/docs/field#rich-text-title
   */
  benefit_content: prismic.RichTextField;
}

/**
 * Default variation for VideoAskSection Slice
 *
 * - **API ID**: `default`
 * - **Description**: Default
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type VideoAskSectionSliceDefault = prismic.SharedSliceVariation<
  "default",
  Simplify<VideoAskSectionSliceDefaultPrimary>,
  Simplify<VideoAskSectionSliceDefaultItem>
>;

/**
 * Slice variation for *VideoAskSection*
 */
type VideoAskSectionSliceVariation = VideoAskSectionSliceDefault;

/**
 * VideoAskSection Shared Slice
 *
 * - **API ID**: `video_ask_section`
 * - **Description**: VideoAskSection
 * - **Documentation**: https://prismic.io/docs/slice
 */
export type VideoAskSectionSlice = prismic.SharedSlice<
  "video_ask_section",
  VideoAskSectionSliceVariation
>;

declare module "@prismicio/client" {
  interface CreateClient {
    (
      repositoryNameOrEndpoint: string,
      options?: prismic.ClientConfig,
    ): prismic.Client<AllDocumentTypes>;
  }

  namespace Content {
    export type {
      BlogPageDocument,
      BlogPageDocumentData,
      BlogPageDocumentDataTagsGroupItem,
      BlogPageDocumentDataSlicesSlice,
      BlogTagsDocument,
      BlogTagsDocumentData,
      EmergencyLocationsDocument,
      EmergencyLocationsDocumentData,
      EmergencyLocationsDocumentDataLocationsItem,
      EmergencyLocationsDocumentDataSlicesSlice,
      FooterDocument,
      FooterDocumentData,
      FooterDocumentDataMenuItem,
      FooterDocumentDataAccreditationsItemsItem,
      HeaderDocument,
      HeaderDocumentData,
      HeaderDocumentDataNavigationMenuItem,
      LandingPageDocument,
      LandingPageDocumentData,
      LandingPageDocumentDataSlicesSlice,
      LandingPageDocumentDataSlices2Slice,
      ReferPageDocument,
      ReferPageDocumentData,
      ReferPageDocumentDataSlicesSlice,
      AllDocumentTypes,
      AdvantagesSectionSlice,
      AdvantagesSectionSliceDefaultPrimary,
      AdvantagesSectionSliceDefaultItem,
      AdvantagesSectionSliceVariation,
      AdvantagesSectionSliceDefault,
      ApplianceSectionSlice,
      ApplianceSectionSliceDefaultPrimary,
      ApplianceSectionSliceDefaultItem,
      ApplianceSectionSliceVariation,
      ApplianceSectionSliceDefault,
      ApproachSectionSlice,
      ApproachSectionSliceDefaultPrimary,
      ApproachSectionSliceDefaultItem,
      ApproachSectionSliceVariation,
      ApproachSectionSliceDefault,
      ArrivalTimeSectionSlice,
      ArrivalTimeSectionSliceDefaultPrimary,
      ArrivalTimeSectionSliceVariation,
      ArrivalTimeSectionSliceDefault,
      BannerSectionSlice,
      BannerSectionSliceDefaultPrimary,
      BannerSectionSliceTextOnlyPrimary,
      BannerSectionSliceVariation,
      BannerSectionSliceDefault,
      BannerSectionSliceTextOnly,
      BecomeMemCardsSlice,
      BecomeMemCardsSliceDefaultPrimary,
      BecomeMemCardsSliceDefaultItem,
      BecomeMemCardsSliceVariation,
      BecomeMemCardsSliceDefault,
      BecomeMemberSectionSlice,
      BecomeMemberSectionSliceDefaultPrimary,
      BecomeMemberSectionSliceDefaultItem,
      BecomeMemberSectionSliceAlternativePrimary,
      BecomeMemberSectionSliceAlternativeItem,
      BecomeMemberSectionSliceVariation,
      BecomeMemberSectionSliceDefault,
      BecomeMemberSectionSliceAlternative,
      BenefitsSectionSlice,
      BenefitsSectionSliceDefaultPrimary,
      BenefitsSectionSliceDefaultItem,
      BenefitsSectionSliceVariation,
      BenefitsSectionSliceDefault,
      BlockWithMoviesSlice,
      BlockWithMoviesSliceDefaultPrimary,
      BlockWithMoviesSliceDefaultItem,
      BlockWithMoviesSliceVariation,
      BlockWithMoviesSliceDefault,
      BlockWithTitleAndContentSlice,
      BlockWithTitleAndContentSliceDefaultPrimary,
      BlockWithTitleAndContentSliceVariation,
      BlockWithTitleAndContentSliceDefault,
      BlogSectionSlice,
      BlogSectionSliceVariation,
      BlogSectionSliceDefault,
      BoilerServiceHeroSlice,
      BoilerServiceHeroSliceDefaultPrimary,
      BoilerServiceHeroSliceImageOnTheRightPrimary,
      BoilerServiceHeroSliceVariation,
      BoilerServiceHeroSliceDefault,
      BoilerServiceHeroSliceImageOnTheRight,
      CardsWithImgOnHoverSlice,
      CardsWithImgOnHoverSliceDefaultPrimary,
      CardsWithImgOnHoverSliceDefaultItem,
      CardsWithImgOnHoverSliceVariation,
      CardsWithImgOnHoverSliceDefault,
      CentralHeatingControlsHeroSlice,
      CentralHeatingControlsHeroSliceDefaultPrimary,
      CentralHeatingControlsHeroSliceCallUsPrimary,
      CentralHeatingControlsHeroSliceVariation,
      CentralHeatingControlsHeroSliceDefault,
      CentralHeatingControlsHeroSliceCallUs,
      ChSUpgradesSectionSlice,
      ChSUpgradesSectionSliceDefaultPrimary,
      ChSUpgradesSectionSliceDefaultItem,
      ChSUpgradesSectionSliceVariation,
      ChSUpgradesSectionSliceDefault,
      CompareFeaturesSectionSlice,
      CompareFeaturesSectionSliceDefaultPrimary,
      CompareFeaturesSectionSliceVariation,
      CompareFeaturesSectionSliceDefault,
      ContactUsSlice,
      ContactUsSliceDefaultPrimary,
      ContactUsSliceVariation,
      ContactUsSliceDefault,
      DiscountBannerSectionSlice,
      DiscountBannerSectionSliceDefaultPrimary,
      DiscountBannerSectionSliceVariation,
      DiscountBannerSectionSliceDefault,
      DividerSlice,
      DividerSliceVariation,
      DividerSliceDefault,
      DocumentWithNavigationAndAccordionsSlice,
      DocumentWithNavigationAndAccordionsSliceDefaultPrimary,
      DocumentWithNavigationAndAccordionsSliceDefaultItem,
      DocumentWithNavigationAndAccordionsSliceVariation,
      DocumentWithNavigationAndAccordionsSliceDefault,
      DocumentWithNavigationSectionSlice,
      DocumentWithNavigationSectionSliceDefaultPrimary,
      DocumentWithNavigationSectionSliceDefaultItem,
      DocumentWithNavigationSectionSliceVariation,
      DocumentWithNavigationSectionSliceDefault,
      FaqsSectionSlice,
      FaqsSectionSliceDefaultPrimary,
      FaqsSectionSliceDefaultItem,
      FaqsSectionSliceVariation,
      FaqsSectionSliceDefault,
      GasServicesSectionSlice,
      GasServicesSectionSliceDefaultPrimary,
      GasServicesSectionSliceDefaultItem,
      GasServicesSectionSliceVariation,
      GasServicesSectionSliceDefault,
      HeadingSlice,
      HeadingSliceDefaultPrimary,
      HeadingSliceVariation,
      HeadingSliceDefault,
      HeatServicesSectionSlice,
      HeatServicesSectionSliceDefaultPrimary,
      HeatServicesSectionSliceDefaultItem,
      HeatServicesSectionSliceVariation,
      HeatServicesSectionSliceDefault,
      HelpAndAdviceSectionSlice,
      HelpAndAdviceSectionSliceDefaultPrimary,
      HelpAndAdviceSectionSliceDefaultItem,
      HelpAndAdviceSectionSliceVariation,
      HelpAndAdviceSectionSliceDefault,
      HeroWithListSlice,
      HeroWithListSliceDefaultPrimary,
      HeroWithListSliceDefaultItem,
      HeroWithListSliceAlternativeColorsPrimary,
      HeroWithListSliceAlternativeColorsItem,
      HeroWithListSliceInvertedPrimary,
      HeroWithListSliceInvertedItem,
      HeroWithListSliceInvertedAlternativePrimary,
      HeroWithListSliceInvertedAlternativeItem,
      HeroWithListSliceVariation,
      HeroWithListSliceDefault,
      HeroWithListSliceAlternativeColors,
      HeroWithListSliceInverted,
      HeroWithListSliceInvertedAlternative,
      HorizontalListSectionSlice,
      HorizontalListSectionSliceDefaultPrimary,
      HorizontalListSectionSliceDefaultItem,
      HorizontalListSectionSliceVariation,
      HorizontalListSectionSliceDefault,
      ImageSlice,
      ImageSliceDefaultPrimary,
      ImageSliceVariation,
      ImageSliceDefault,
      ListWithSwitchSlice,
      ListWithSwitchSliceDefaultPrimary,
      ListWithSwitchSliceDefaultItem,
      ListWithSwitchSliceVariation,
      ListWithSwitchSliceDefault,
      LocateUsSectionSlice,
      LocateUsSectionSliceDefaultPrimary,
      LocateUsSectionSliceVariation,
      LocateUsSectionSliceDefault,
      LondonPlumbingAreasSlice,
      LondonPlumbingAreasSliceDefaultPrimary,
      LondonPlumbingAreasSliceDefaultItem,
      LondonPlumbingAreasSliceVariation,
      LondonPlumbingAreasSliceDefault,
      MapSectionSlice,
      MapSectionSliceVariation,
      MapSectionSliceDefault,
      MarqueeSectionSlice,
      MarqueeSectionSliceDefaultPrimary,
      MarqueeSectionSliceDarkPrimary,
      MarqueeSectionSliceVariation,
      MarqueeSectionSliceDefault,
      MarqueeSectionSliceDark,
      ParagraphSlice,
      ParagraphSliceDefaultPrimary,
      ParagraphSliceVariation,
      ParagraphSliceDefault,
      PartnersSectionSlice,
      PartnersSectionSliceDefaultPrimary,
      PartnersSectionSliceDefaultItem,
      PartnersSectionSliceVariation,
      PartnersSectionSliceDefault,
      PeacePlanAdvantagesSlice,
      PeacePlanAdvantagesSliceDefaultPrimary,
      PeacePlanAdvantagesSliceDefaultItem,
      PeacePlanAdvantagesSliceVariation,
      PeacePlanAdvantagesSliceDefault,
      PeacePlanCtaSlice,
      PeacePlanCtaSliceDefaultPrimary,
      PeacePlanCtaSliceVariation,
      PeacePlanCtaSliceDefault,
      PeacePlanDescriptionWithTogglerSlice,
      PeacePlanDescriptionWithTogglerSliceDefaultPrimary,
      PeacePlanDescriptionWithTogglerSliceVariation,
      PeacePlanDescriptionWithTogglerSliceDefault,
      PeacePlanSubscriptionBenefitsSlice,
      PeacePlanSubscriptionBenefitsSliceDefaultPrimary,
      PeacePlanSubscriptionBenefitsSliceDefaultItem,
      PeacePlanSubscriptionBenefitsSliceVariation,
      PeacePlanSubscriptionBenefitsSliceDefault,
      PhoneAnimationSectionSlice,
      PhoneAnimationSectionSliceDefaultPrimary,
      PhoneAnimationSectionSliceDefaultItem,
      PhoneAnimationSectionSliceVariation,
      PhoneAnimationSectionSliceDefault,
      PlansSectionSlice,
      PlansSectionSliceDefaultPrimary,
      PlansSectionSliceDefaultItem,
      PlansSectionSliceVariation,
      PlansSectionSliceDefault,
      PlumbingServicesSectionSlice,
      PlumbingServicesSectionSliceDefaultPrimary,
      PlumbingServicesSectionSliceDefaultItem,
      PlumbingServicesSectionSliceVariation,
      PlumbingServicesSectionSliceDefault,
      PreviewSectionSlice,
      PreviewSectionSliceDefaultPrimary,
      PreviewSectionSliceDefaultItem,
      PreviewSectionSliceHeroPrimary,
      PreviewSectionSliceHeroItem,
      PreviewSectionSliceWithoutReferringPrimary,
      PreviewSectionSliceWithoutReferringItem,
      PreviewSectionSliceSliderItem,
      PreviewSectionSliceVariation,
      PreviewSectionSliceDefault,
      PreviewSectionSliceHero,
      PreviewSectionSliceWithoutReferring,
      PreviewSectionSliceSlider,
      ProtectingHouseholdsSlice,
      ProtectingHouseholdsSliceDefaultPrimary,
      ProtectingHouseholdsSliceDefaultItem,
      ProtectingHouseholdsSliceVariation,
      ProtectingHouseholdsSliceDefault,
      ProtectingHouseholdsSectionSlice,
      ProtectingHouseholdsSectionSliceDefaultPrimary,
      ProtectingHouseholdsSectionSliceDefaultItem,
      ProtectingHouseholdsSectionSliceVariation,
      ProtectingHouseholdsSectionSliceDefault,
      RecentWorkSlice,
      RecentWorkSliceDefaultPrimary,
      RecentWorkSliceDefaultItem,
      RecentWorkSliceVariation,
      RecentWorkSliceDefault,
      ReferAnEngineerSectionSlice,
      ReferAnEngineerSectionSliceDefaultPrimary,
      ReferAnEngineerSectionSliceVariation,
      ReferAnEngineerSectionSliceDefault,
      ReferAndEarnSlice,
      ReferAndEarnSliceDefaultPrimary,
      ReferAndEarnSliceVariation,
      ReferAndEarnSliceDefault,
      ReferralBonusSectionSlice,
      ReferralBonusSectionSliceDefaultPrimary,
      ReferralBonusSectionSliceDefaultItem,
      ReferralBonusSectionSliceVariation,
      ReferralBonusSectionSliceDefault,
      ReviewsSectionSlice,
      ReviewsSectionSliceDefaultPrimary,
      ReviewsSectionSliceDefaultItem,
      ReviewsSectionSliceDarkBackgroundPrimary,
      ReviewsSectionSliceDarkBackgroundItem,
      ReviewsSectionSliceVariation,
      ReviewsSectionSliceDefault,
      ReviewsSectionSliceDarkBackground,
      ServicesSectionSlice,
      ServicesSectionSliceDefaultItem,
      ServicesSectionSliceVariation,
      ServicesSectionSliceDefault,
      ShuffledCardsListSlice,
      ShuffledCardsListSliceDefaultPrimary,
      ShuffledCardsListSliceDefaultItem,
      ShuffledCardsListSliceWithoutMarqueePrimary,
      ShuffledCardsListSliceWithoutMarqueeItem,
      ShuffledCardsListSliceVariation,
      ShuffledCardsListSliceDefault,
      ShuffledCardsListSliceWithoutMarquee,
      StepsSectionSlice,
      StepsSectionSliceDefaultPrimary,
      StepsSectionSliceDefaultItem,
      StepsSectionSliceVariation,
      StepsSectionSliceDefault,
      SubscribtionsSectionSlice,
      SubscribtionsSectionSliceDefaultPrimary,
      SubscribtionsSectionSliceDefaultItem,
      SubscribtionsSectionSliceVariation,
      SubscribtionsSectionSliceDefault,
      TextAndImageSectionSlice,
      TextAndImageSectionSliceDefaultPrimary,
      TextAndImageSectionSliceVariation,
      TextAndImageSectionSliceDefault,
      TopBrandsSectionSlice,
      TopBrandsSectionSliceDefaultPrimary,
      TopBrandsSectionSliceDefaultItem,
      TopBrandsSectionSliceVariation,
      TopBrandsSectionSliceDefault,
      TopServicesSlice,
      TopServicesSliceDefaultPrimary,
      TopServicesSliceDefaultItem,
      TopServicesSliceVariation,
      TopServicesSliceDefault,
      UpgrageYourChCSlice,
      UpgrageYourChCSliceDefaultPrimary,
      UpgrageYourChCSliceSecondPrimary,
      UpgrageYourChCSliceThirdPrimary,
      UpgrageYourChCSliceFourthPrimary,
      UpgrageYourChCSliceVariation,
      UpgrageYourChCSliceDefault,
      UpgrageYourChCSliceSecond,
      UpgrageYourChCSliceThird,
      UpgrageYourChCSliceFourth,
      VideoAskSectionSlice,
      VideoAskSectionSliceDefaultPrimary,
      VideoAskSectionSliceDefaultItem,
      VideoAskSectionSliceVariation,
      VideoAskSectionSliceDefault,
    };
  }
}
